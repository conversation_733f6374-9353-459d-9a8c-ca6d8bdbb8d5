#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的UWB定位算法
"""

import numpy as np
import sys
import os

# 模拟UWBCalibration类的核心方法
class MockUWBCalibration:
    def _get_robust_initial_position(self, anchors, distances, z_fixed):
        """
        获取鲁棒的初始位置估计
        """
        candidates = []
        
        # 方法1: 锚点中心
        center = np.mean(anchors[:, :2], axis=0)
        candidates.append(center)
        
        # 方法2: 加权中心（距离越近权重越大）
        weights = 1.0 / (distances + 0.1)  # 避免除零
        weights /= np.sum(weights)
        weighted_center = np.sum(anchors[:, :2] * weights.reshape(-1, 1), axis=0)
        candidates.append(weighted_center)
        
        # 方法3: 尝试线性最小二乘（如果有足够锚点）
        if len(anchors) >= 3:
            try:
                linear_pos = self._linear_least_squares_2d(anchors, distances, z_fixed)
                if np.all(np.isfinite(linear_pos)):
                    candidates.append(linear_pos)
            except:
                pass
        
        # 评估候选位置，选择残差最小的
        best_pos = center  # 默认值
        best_residual = float('inf')
        
        for candidate in candidates:
            pos_3d = np.array([candidate[0], candidate[1], z_fixed])
            pred_distances = np.linalg.norm(anchors - pos_3d, axis=1)
            residual = np.sqrt(np.mean((distances - pred_distances)**2))
            
            if residual < best_residual:
                best_residual = residual
                best_pos = candidate
        
        return best_pos
    
    def _linear_least_squares_2d(self, anchors, distances, z_fixed):
        """
        2D线性最小二乘求解（固定z）
        """
        N = len(anchors)
        if N < 3:
            raise ValueError("锚点数量不足")
        
        # 使用第一个锚点作为参考
        ref_anchor = anchors[0]
        ref_dist = distances[0]
        
        A = []
        b = []
        
        for i in range(1, N):
            anchor_i = anchors[i]
            dist_i = distances[i]
            
            # 构建线性方程
            coeff = 2 * (anchor_i[:2] - ref_anchor[:2])
            rhs = (np.dot(anchor_i, anchor_i) - np.dot(ref_anchor, ref_anchor) - 
                   (dist_i**2 - ref_dist**2) + 
                   (anchor_i[2] - z_fixed)**2 - (ref_anchor[2] - z_fixed)**2)
            
            A.append(coeff)
            b.append(rhs)
        
        A = np.array(A)
        b = np.array(b)
        
        # 求解
        xy_solution, _, _, _ = np.linalg.lstsq(A, b, rcond=None)
        
        return xy_solution

    def robust_localize_fixed(self, anchors, distances, z_fixed=0.56,
                             method="huber", delta=2.0, max_iter=20, tol=1e-4):
        """
        修复后的鲁棒定位算法
        """
        print("=== 修复后的鲁棒定位算法 ===")
        
        # 数据预处理和异常值检测
        print(f"原始数据: 锚点数={len(anchors)}, 距离={distances}")
        
        # 检测明显异常的测距值
        valid_mask = np.ones(len(distances), dtype=bool)
        
        # 基于距离范围的筛选
        range_mask = (distances >= 0.1) & (distances <= 50.0)
        if not np.all(range_mask):
            print(f"⚠️ 发现超出合理范围的距离: {distances[~range_mask]}")
            valid_mask &= range_mask
        
        # 检查与锚点几何的一致性
        for i in range(len(anchors)):
            if not valid_mask[i]:
                continue
            min_anchor_dist = float('inf')
            for j in range(len(anchors)):
                if i != j:
                    anchor_dist = np.linalg.norm(anchors[i] - anchors[j])
                    min_anchor_dist = min(min_anchor_dist, float(anchor_dist))
            
            # 如果测距值远大于最近锚点间距离，标记为可疑
            if distances[i] > min_anchor_dist * 1.5:
                print(f"⚠️ 锚点{i}测距值{distances[i]:.3f}m 可能异常（最近锚点间距{min_anchor_dist:.3f}m）")
                # 暂时不排除，但降低权重
        
        N = len(anchors)
        if N < 3:
            raise ValueError("有效锚点不足 3 个，无法定位")
        
        # 改进的初值估计
        pos_xy = self._get_robust_initial_position(anchors, distances, z_fixed)
        print(f"初始位置估计: [{pos_xy[0]:.3f}, {pos_xy[1]:.3f}]")
        
        # 权重初始化
        weights = np.ones(N)
        
        def compute_residuals(pos_xy):
            pos = np.array([pos_xy[0], pos_xy[1], z_fixed])
            pred = np.linalg.norm(anchors - pos, axis=1)
            return distances - pred
        
        # 记录收敛历史
        prev_cost = float('inf')
        
        # IRLS 迭代
        for it in range(max_iter):
            pos = np.array([pos_xy[0], pos_xy[1], z_fixed])
            r = compute_residuals(pos_xy)
            
            # 计算当前代价函数值
            current_cost = np.sum(weights * r**2)
            
            # 更新权重
            if method == "huber":
                weights = np.where(np.abs(r) <= delta, 1.0, delta / (np.abs(r) + 1e-8))
            elif method == "tukey":
                mask = np.abs(r) < delta
                weights = np.zeros_like(r)
                weights[mask] = (1 - (r[mask] / delta) ** 2) ** 2
            
            # 构建加权最小二乘问题
            A = []
            b = []
            for i in range(N):
                dx = pos[0] - anchors[i, 0]
                dy = pos[1] - anchors[i, 1]
                dz = pos[2] - anchors[i, 2]
                pred_dist = np.sqrt(dx**2 + dy**2 + dz**2)
                
                if pred_dist > 1e-6:
                    # 雅可比矩阵：对 x, y 的偏导数（注意符号）
                    jx = -dx / pred_dist  # 负号很重要！
                    jy = -dy / pred_dist
                    
                    # 加权雅可比和残差
                    w_sqrt = np.sqrt(weights[i])
                    A.append([w_sqrt * jx, w_sqrt * jy])
                    b.append(w_sqrt * r[i])
                else:
                    A.append([np.sqrt(weights[i]), 0])
                    b.append(np.sqrt(weights[i]) * r[i])
            
            A = np.array(A)
            b = np.array(b)
            
            # 解正规方程
            try:
                delta_xy, _, _, _ = np.linalg.lstsq(A, b, rcond=None)
                
                # 步长控制：防止发散
                max_step = 2.0  # 最大步长2米
                step_norm = np.linalg.norm(delta_xy)
                if step_norm > max_step:
                    delta_xy = delta_xy * (max_step / step_norm)
                    print(f"迭代 {it+1}: 限制步长从 {step_norm:.3f}m 到 {max_step}m")
                
                pos_xy_new = pos_xy + delta_xy
            except np.linalg.LinAlgError:
                print(f"迭代 {it+1}: 线性方程求解失败")
                break
            
            # 检查收敛条件
            pos_change = np.linalg.norm(delta_xy)
            cost_change = abs(current_cost - prev_cost) / (prev_cost + 1e-8)
            
            print(f"迭代 {it+1}: 位置变化={pos_change:.6f}, 代价变化={cost_change:.6f}, RMS残差={np.sqrt(np.mean(r**2)):.4f}")
            
            # 更新位置
            pos_xy = pos_xy_new
            prev_cost = current_cost
            
            # 收敛判断
            if pos_change < tol and cost_change < tol:
                print(f"收敛于迭代 {it+1}")
                break
            elif it == max_iter - 1:
                print(f"达到最大迭代次数 {max_iter}")
        
        # 最终估计
        est = np.array([pos_xy[0], pos_xy[1], z_fixed])
        final_residuals = compute_residuals(pos_xy)
        
        # 打印残差信息
        print("\n=== 残差分析 ===")
        for i in range(N):
            pred = np.linalg.norm(anchors[i] - est)
            print(f"锚点{i}: 实测={distances[i]:.3f}, 预测={pred:.3f}, 残差={final_residuals[i]:.3f}, 权重={weights[i]:.2f}")
        
        rms_residual = np.sqrt(np.mean(final_residuals**2))
        print(f"RMS残差: {rms_residual:.4f}m")
        
        return est, weights, it + 1

def test_with_problematic_data():
    """
    使用有问题的实际数据测试修复后的算法
    """
    print("=== 测试修复后的算法 ===\n")
    
    # 你的实际数据
    anchors = np.array([
        [-0.15, 2.19, 1.4],
        [-6.2474, -8.8274, 2.38], 
        [-3.2080, -3.1134, 2.68]
    ])
    
    distances = np.array([1.785, 3.750, 6.380])  # 注意：锚点1的距离明显异常
    true_pos = np.array([0.765, 0.8948, 0.56])
    
    print("输入数据:")
    print(f"锚点坐标:\n{anchors}")
    print(f"测量距离: {distances}")
    print(f"真实位置: {true_pos}")
    print()
    
    # 创建测试实例
    calibrator = MockUWBCalibration()
    
    try:
        est, weights, n_iter = calibrator.robust_localize_fixed(
            anchors, distances,
            z_fixed=0.56,
            method="huber",
            delta=2.0,
            max_iter=15,
            tol=1e-5
        )
        
        print(f"\n最终估计位置: [{est[0]:.3f}, {est[1]:.3f}, {est[2]:.3f}]")
        print(f"与真实位置的误差: {np.linalg.norm(est[:2] - true_pos[:2]):.3f}m")
        print(f"总迭代次数: {n_iter}")
        
    except Exception as e:
        print(f"算法执行失败: {e}")

if __name__ == "__main__":
    test_with_problematic_data()
    
    print("\n=== 主要修复点 ===")
    print("1. 添加异常值检测和数据预处理")
    print("2. 修复雅可比矩阵符号错误（添加负号）")
    print("3. 改进初始位置估计（多种方法+选择最优）")
    print("4. 添加步长控制防止算法发散")
    print("5. 更严格的收敛判断条件")
    print("6. 详细的调试信息输出")
