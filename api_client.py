# # api_client.py

# import requests
# import base64
# import yaml
# import os
# import json

# class NavigationAPIClient:
#     """
#     A client class to interact with the Navigation Server API.
#     """
#     def __init__(self, server_url):
#         """
#         Initializes the API client.

#         :param server_url: The base URL of the navigation server (e.g., "http://localhost:10000").
#         """
#         if server_url.endswith('/'):
#             self.server_url = server_url[:-1]
#         else:
#             self.server_url = server_url
#         self.headers = {"Content-Type": "application/json"}

#     def _make_request(self, method, endpoint, **kwargs):
#         """Helper method to make HTTP requests and handle common errors."""
#         url = f"{self.server_url}/{endpoint}"
#         try:
#             response = requests.request(method, url, **kwargs)
#             response.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
#             return response.json()
#         except requests.exceptions.HTTPError as e:
#             print(f"HTTP Error for {method.upper()} {url}: {e}")
#         except requests.exceptions.ConnectionError as e:
#             print(f"Connection Error for {method.upper()} {url}: {e}")
#         except requests.exceptions.Timeout as e:
#             print(f"Timeout Error for {method.upper()} {url}: {e}")
#         except requests.exceptions.RequestException as e:
#             print(f"An unexpected error occurred with the request: {e}")
#         except json.JSONDecodeError:
#             print(f"Failed to decode JSON from response. Response text: {response.text}")
#         return None

#     def download_map(self, map_name, save_dir, pgm=True, yaml_file=True, pcd=True):
#         """Downloads map files (PGM, YAML, PCD) from the server."""
#         params = {
#             'map_name': map_name,
#             "pgm_need": pgm,
#             "yaml_need": yaml_file,
#             "pcd_need": pcd
#         }
#         data = self._make_request('get', 'downloadMap', params=params)

#         if data and data.get('code') == 0 and 'data' in data:
#             if not os.path.exists(save_dir):
#                 os.makedirs(save_dir)
            
#             map_data = data['data']
#             # Save files
#             if map_data.get('yaml_data'):
#                 with open(os.path.join(save_dir, f"{map_name}.yaml"), 'w') as f:
#                     yaml.dump(map_data['yaml_data'], f)
#             if map_data.get('pgm_data'):
#                 with open(os.path.join(save_dir, f"{map_name}.pgm"), 'wb') as f:
#                     f.write(base64.b64decode(map_data['pgm_data']))
#             if map_data.get('pcd_data'):
#                 with open(os.path.join(save_dir, f"{map_name}.pcd"), 'wb') as f:
#                     f.write(base64.b64decode(map_data['pcd_data']))
#             return data
#         return data

#     def load_map(self, map_name):
#         """Requests the server to load a specific map."""
#         payload = {"map_name": map_name}
#         return self._make_request('post', 'loadMap', json=payload, headers=self.headers)

#     def get_localization(self):
#         """Retrieves the robot's current localization info."""
#         return self._make_request('get', 'getLocalization')

#     def get_navi_status(self):
#         """Retrieves the robot's current navigation status."""
#         return self._make_request('get', 'getNaviStatus')

#     def set_goals(self, goal_list):
#         """Sets a list of navigation goals for the robot."""
#         payload = {"goal_list": goal_list}
#         return self._make_request('post', 'setGoals', json=payload, headers=self.headers)

#     def set_initial_pose(self, x, y, yaw):
#         """Sets the initial pose of the robot on the map."""
#         payload = {"x": x, "y": y, "yaw": yaw}
#         return self._make_request('post', 'setInitialPose', json=payload, headers=self.headers)

#     def get_current_id(self):
#         """Gets the current goal ID and the list of all goals."""
#         return self._make_request('get', 'getCurrentID')

#     def go_next_goal(self):
#         """Commands the robot to proceed to the next goal in the list."""
#         return self._make_request('post', 'goNextGoal')



# api_client.py

import requests
import base64
import yaml
import os
import json

class NavigationAPIClient:
    """
    A client class to interact with the Navigation Server API.
    The method implementations are based on the original scripts.
    """
    def __init__(self, server_url):
        """
        Initializes the API client.

        :param server_url: The base URL of the navigation server (e.g., "http://localhost:10000").
        """
        if server_url.endswith('/'):
            self.server_url = server_url[:-1]
        else:
            self.server_url = server_url


        print(f"+++++++++++++++url:{self.server_url}")
        \


    def download_map(self, map_name, save_dir):
        """
        Tests the /downloadMap interface to download map data from the server.
        """
        url = f"{self.server_url}/downloadMap"
        params = {
            'map_name': map_name, 
            "pgm_need": True, 
            "yaml_need": True, 
            "pcd_need": True
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()  # If the request fails (non-2xx status code), an exception is thrown
            
            data = response.json()
            
            if data.get('code') == 0:
                print("地图加载成功！")
                map_data = data.get('data', {})
                
                # Create save directory
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)
                    print(f"Created directory: {save_dir}")

                # Process and save YAML file
                yaml_data = map_data.get('yaml_data')
                if yaml_data:
                    yaml_file_path = os.path.join(save_dir, f"{map_name}.yaml")
                    with open(yaml_file_path, 'w', encoding='utf-8') as f:
                        yaml.dump(yaml_data, f)
                    print(f"YAML file saved to: {yaml_file_path}")

                # Process and save PGM file
                pgm_data = map_data.get('pgm_data')
                if pgm_data:
                    pgm_file_path = os.path.join(save_dir, f"{map_name}.pgm")
                    with open(pgm_file_path, 'wb') as f:
                        f.write(base64.b64decode(pgm_data))
                    print(f"PGM file saved to: {pgm_file_path}")

                # Process and save PCD file
                pcd_data = map_data.get('pcd_data')
                if pcd_data:
                    pcd_file_path = os.path.join(save_dir, f"{map_name}.pcd")
                    with open(pcd_file_path, 'wb') as f:
                        f.write(base64.b64decode(pcd_data))
                    print(f"PCD file saved to: {pcd_file_path}")
            else:
                print(f"动图下载失败，错误码: {data.get('code')}, 响应: {data.get('message')}")
            
            return data

        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except Exception as e:
            print(f"An error occurred while downloading or processing map data: {e}")
        return None

    def load_map(self, map_name):
        """
        Tests the /loadMap interface to load a specified map.
        """
        url = f"{self.server_url}/loadMap"
        payload = {
            "map_name": map_name
            }
        headers = {"Content-Type": "application/json"}
        
        try:
            #response = requests.post(url, data=json.dumps(payload), headers=headers)
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                print(f"地图加载成功！响应: '{data.get('message')}'")
            else:
                print(f"地图加载失败！错误码: {data.get('code')}, 响应: '{data.get('message')}'")
            return data
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except json.JSONDecodeError:
            print("The response is not in a valid JSON format.")
        except Exception as e:
            print(f"An unknown error occurred during the request: {e}")
        return None

    def get_localization(self):
        """
        Tests the /getLocalization interface to get the robot's localization information.
        """
        url = f"{self.server_url}/getLocalization"

        try:
            response = requests.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                localization_info = data.get('data', {}).get('localization_info')
                print(f"获取位置成功: {localization_info}")
            else:
                print(f"获取位置失败，错误码: {data.get('code')}, 响应: '{data.get('message')}'")
            return data
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except json.JSONDecodeError:
            print("The response is not in a valid JSON format.")
        except Exception as e:
            print(f"An unknown error occurred during the request: {e}")
        return None

    def get_navi_status(self):
        """
        Tests the /getNaviStatus interface to get the robot's navigation status.
        """
        url = f"{self.server_url}/getNaviStatus"

        try:
            response = requests.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                status_map = {0: "wait for goal", 1: "running", 3: "success", 4: "failed"}
                nav_status_code = data.get('data', {}).get('navigation_status')
                status_desc = status_map.get(nav_status_code, "Unknown status")
                print(f"获取导航状态成功: {nav_status_code} ({status_desc})")
            else:
                print(f"请求失败，错误码: {data.get('code')}, 响应: '{data.get('message')}'")
            return data

        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except json.JSONDecodeError:
            print("The response is not in a valid JSON format.")
        except Exception as e:
            print(f"An unknown error occurred during the request: {e}")
        return None

    def set_goals(self, goal_list):
        """
        Tests the /setGoals interface to set the robot's target points.
        """
        url = f"{self.server_url}/setGoals"
        payload = {
            "goal_list": goal_list
            }
        headers = {"Content-Type": "application/json"}
        
        try:
            #response = requests.post(url, data=json.dumps(payload), headers=headers)
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()
            
            data = response.json()

            if data.get('code') == 0:
                print(f"目标设置成功！响应: '{data.get('message')}'")
            else:
                print(f"目标设置失败！状态码: {data.get('code')}, 响应: '{data.get('message')}'")
            return data

        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except json.JSONDecodeError:
            print("The response is not in a valid JSON format.")
        except Exception as e:
            print(f"An unknown error occurred during the request: {e}")
        return None

    def set_initial_pose(self, x, y, yaw):
        """
        Tests the /setInitialPose interface to set the robot's initial pose.
        """
        url = f"{self.server_url}/setInitialPose"
        payload = {
            "x": x, 
            "y": y, 
            "yaw": yaw
        }
        #headers = {"Content-Type": "application/json"}

        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()

            if data.get('code') == 0:
                print(f"Initial pose set successfully! Response message: '{data.get('message')}'")
            else:
                print(f"Failed to set initial pose, error code: {data.get('code')}, message: '{data.get('message')}'")
            return data

        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except json.JSONDecodeError:
            print("The response is not in a valid JSON format.")
        except Exception as e:
            print(f"An unknown error occurred during the request: {e}")
        return None

    def get_current_id(self):
        """
        Tests the /getCurrentID interface to get the current target ID and all target point information.
        """
        url = f"{self.server_url}/getCurrentID"
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                current_id = data.get('data', {}).get('current_goal_id')
                goal_list = data.get('data', {}).get('current_goal_list')
                print("Successfully retrieved current target information:")
                print(f"  - Current target ID (current_goal_id): {current_id}")
                print(f"  - Complete target list (current_goal_list): {json.dumps(goal_list, indent=4)}")
            else:
                print(f"Failed to retrieve current target ID, error code: {data.get('code')}, message: '{data.get('message')}'")
            return data

        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except json.JSONDecodeError:
            print("The response is not in a valid JSON format.")
        except Exception as e:
            print(f"An unknown error occurred during the request: {e}")
        return None

    def go_next_goal(self):
        """
        Tests the /goNextGoal interface to issue a command to navigate to the next target point.
        """
        url = f"{self.server_url}/goNextGoal"
        
        try:
            response = requests.post(url)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                print(f"Command to navigate to the next target point sent successfully! Response message: '{data.get('message')}'")
            else:
                print(f"Failed to send command, error code: {data.get('code')}, message: '{data.get('message')}'")
            return data
            
        except requests.exceptions.RequestException as e:
            print(f"Request failed, a network error occurred: {e}")
        except json.JSONDecodeError:
            print("The response is not in a valid JSON format.")
        except Exception as e:
            print(f"An unknown error occurred during the request: {e}")
        return None



    def get_map_list(self):
        """
        测试 /getMapList 接口。
        
        :param server_url: 服务器的基础 URL，例如 "http://localhost:10000"
        """
        url = f"{self.server_url}/getMapList"
        
        try:
            response = requests.get(url)
            data = response.json()
            if data['code'] == 0:
                print("获取地图列表成功：", data['data']['map_list'])
            else:
                print(f"请求失败，错误码: {data['code']}, 响应: {data['message']}")
        except Exception as e:
            print(f"请求失败: {e}")

    def relocalized_success(self):
        """
        测试 /relocalizedSuccess 接口。
        
        :param server_url: 服务器的基础 URL，例如 "http://localhost:10000"
        """
        url = f"{self.server_url}/relocalizedSuccess"
        
        print("dfdffd")
        try:
            response = requests.post(url)
            data = response.json()
            if data['code'] == 0:
                print("重定位成功设置成功")
            else:
                print(f"请求失败，错误码: {data['code']}, 响应: {data['message']}")
        except Exception as e:
            print(f"请求失败: {e}")


    def load_task(self, task_name):
        """
        测试 /loadTask 接口。
        
        :param server_url: 服务器的基础 URL，例如 "http://localhost:10000"
        :param task_name: 要加载的任务名称
        """
        url = f"{self.server_url}/loadTask"
        payload = {
            "task_name": task_name
        }
        
        try:
            response = requests.post(url, json=payload)
            data = response.json()
            if data['code'] == 0:
                print(f"任务加载成功：{data['message']}")
            else:
                print(f"加载失败，错误码: {data['code']}, 响应: {data['message']}")
        except Exception as e:
            print(f"请求失败: {e}")


    def goto_goal(self, goal_id):
        """
        测试 /goto 接口。
        
        :param server_url: 服务器的基础 URL，例如 "http://localhost:10000"
        :param goal_id: 目标点ID
        """
        url = f"{self.server_url}/goto"
        payload = {
            "goal_id": goal_id
        }
        headers = {"Content-Type": "application/json"}
        
        # try:
        response = requests.post(url, json=payload, headers=headers)
        data = response.json()
        if data['code'] == 0:
            print("导航指令发送成功！响应：", data['message'])
        else:
            print(f"导航指令发送失败！错误码：{data['code']}，响应：{data['message']}")
        # except Exception as e:
        #     print(f"请求失败：{e}")


    def set_goals(self, goal_list):
        """
        测试 setGoals 接口。
        
        :param server_url: 服务器的基础 URL，例如 "http://localhost:10000"
        :param goal_list: 包含目标点的列表，每个目标点是一个字典，包含 x, y, theta
        """
        # 构造请求数据
        payload = {
            "goal_list": goal_list
        }

        # 发送 POST 请求
        url = f"{self.server_url}/setGoals"
        headers = {"Content-Type": "application/json"}
        try:
            response = requests.post(url, json=payload, headers=headers)
            data = response.json()
            # 打印响应结果
            if data['code'] == 0:
                print(f"目标点设置成功！响应：{data['message']}")
            else:
                print(f"目标点设置失败！状态码：{data['code']}，响应：{data['message']}")
        except Exception as e:
            print(f"请求失败：{e}")


    