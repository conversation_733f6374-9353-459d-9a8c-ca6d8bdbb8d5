import serial
import time
import numpy as np
from scipy.optimize import least_squares

# ---------------- 工具函数 ----------------
def huber_loss(r, delta=0.5):
    """Huber鲁棒残差缩放"""
    return np.where(np.abs(r) <= delta, r, delta * np.sign(r))

def calibrate_anchors_with_init(poses, ranges, anchors_init, estimate_bias=True, huber_delta=0.5, lam=0.1):
    """
    双阶段锚点标定
    ----------
    poses : ndarray (M,3)
        小车已知位置 (M个点, z固定=1.29)
    ranges : ndarray (N,M)
        测距数据 (N个锚 × M个点)
    anchors_init : ndarray (N,3)
        初始锚点坐标
    estimate_bias : bool
        是否估计偏置 b_i
    huber_delta : float
        Huber损失阈值
    lam : float
        偏置L1正则化系数
    """
    N, M = ranges.shape
    d = 3
    anchors0 = np.array(anchors_init, dtype=float)
    biases0 = np.zeros(N)

    # 参数向量
    x0 = np.hstack([anchors0.ravel(), biases0])

    def residuals(params):
        anchors = params[:N*d].reshape(N, d)
        biases = params[N*d:] if estimate_bias else np.zeros(N)
        res = []
        for i in range(N):
            pred = np.linalg.norm(poses - anchors[i], axis=1) + biases[i]
            r = ranges[i] - pred
            r = huber_loss(r, delta=huber_delta)
            res.extend(r)
        if estimate_bias and lam > 0:
            res.extend(np.sqrt(lam) * biases)
        return np.array(res)

    result = least_squares(residuals, x0, method="lm", max_nfev=200)
    anchors_hat = result.x[:N*d].reshape(N, d)
    biases_hat = result.x[N*d:] if estimate_bias else np.zeros(N)
    return anchors_hat, biases_hat, result

# ---------------- 串口通信函数 ----------------
def request_distance(ser, mac, count=10):
    """向标签请求与某个锚点的距离"""
    cmd = f"$mac,{mac},{count}\r\n"
    ser.write(cmd.encode())
    time.sleep(0.1)
    data = ser.readline().decode().strip()
    return parse_distance(mac, data)

def parse_distance(mac, data_str):
    """解析串口返回的数据（⚠️需根据实际协议格式修改）"""
    try:
        # 假设返回格式: "d41b9a245222,3.52"
        parts = data_str.split(",")
        if parts[0] == mac:
            return float(parts[1])
        else:
            return None
    except Exception:
        return None

# ---------------- 主流程 ----------------
if __name__ == "__main__":
    # 1. 初始锚点坐标 (粗解, 配置表)
    anchors_mac = [
        "d4599a149221",
        "d4da8324d322",
        "d422685c5222",
        "d41b9a245222",
    ]
    anchors_init = np.array([
        [-5.31, 10.664, 2.38],
        [ 3.739,  2.7146, 2.785],
        [-8.6106, 2.3646, 2.54],
        [-0.9106,-1.3353, 1.47],
    ])

    # 2. 串口初始化
    ser = serial.Serial("/dev/ttyUSB0", baudrate=115200, timeout=1)

    # 3. 数据采集
    dataset = []
    while True:
        input("小车到达目标位置后按 Enter 确认...")
        # ⚠️这里需要从小车系统获取当前位置 (x,y)，z固定=1.29
        # 例如: car_pos = get_car_position() 
        # 现在用手动输入代替:
        x = float(input("输入小车X坐标: "))
        y = float(input("输入小车Y坐标: "))
        car_pos = np.array([x, y, 1.29])

        measurements = []
        for mac in anchors_mac:
            dist = request_distance(ser, mac, count=10)
            if dist is not None:
                measurements.append(dist)
                print(f"Anchor {mac} -> {dist:.3f} m")
            else:
                measurements.append(np.nan)

        dataset.append((car_pos, measurements))
        if input("继续采集? (y/n): ") == "n":
            break

    ser.close()

    # 4. 转换为矩阵
    poses = np.array([d[0] for d in dataset])
    ranges = np.array([d[1] for d in dataset]).T  # (N,M)

    # 5. 执行二阶段优化
    anchors_hat, biases_hat, _ = calibrate_anchors_with_init(
        poses, ranges, anchors_init,
        estimate_bias=True, huber_delta=0.5, lam=0.1
    )

    # 6. 输出结果
    print("\n===== 标定结果 =====")
    for mac, init, hat, b in zip(anchors_mac, anchors_init, anchors_hat, biases_hat):
        print(f"Anchor {mac}")
        print(f"  初始: {init}")
        print(f"  标定: {hat}")
        print(f"  偏置: {b:.3f} m\n")

    # 7. 保存结果
    np.savez("anchors_calibrated.npz", anchors=anchors_hat, biases=biases_hat, macs=anchors_mac)
    print("结果已保存 anchors_calibrated.npz")
