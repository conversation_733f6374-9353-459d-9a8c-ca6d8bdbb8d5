#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
uwb_calibration_full.py

单文件：UWB 标定（采集 + 双阶段优化）
保留你现有的串口/导航 API 逻辑，仅替换优化核心（线性初值 + 鲁棒非线性精化）。
输出：anchors_calibrated.npz + 可选 3D 可视化
"""

import serial
import time
import numpy as np
from scipy.optimize import least_squares
from api_client import NavigationAPIClient   # 假定你已有此模块
import json
from collections import deque
import threading
import queue
import statistics
import math
import sys

# 可视化（可选）
try:
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D  # noqa: F401
    HAS_MPL = True
except Exception:
    HAS_MPL = False


class UWBCalibration:
    def __init__(self, server_url, serial_port, baudrate=115200):
        """
        初始化 UWB 标定对象
        注意：保持串口/数据读取逻辑与你原脚本一致
        """
        # API 客户端（你原始脚本使用的）
        self.api_client = NavigationAPIClient(server_url)

        # 串口参数
        self.serial_port = serial_port
        self.baudrate = baudrate
        self.ser = None

        # 你提供的锚点 MAC 列表与粗略初值（可以只写部分，N 必须匹配）
        self.anchors_mac = [
            "d4cf8914d31a",
            "d4599a149221",
            "d4da8324d322",
            "d422685c5222",
            "d41b9a245222",
        ]
        self.anchors_init = np.array([
            [-0.15, 2.19, 1.4],
            [-6.2474, -8.8274, 2.38],
            [-3.2080, -3.1134, 2.68],
            [1.3064,-6.9271, 2.485],
            [2.7504, 0.4234, 1.47],
        ], dtype=float)

        # 数据缓冲 / 状态
        self.position_buffer = deque(maxlen=50)
        self.distance_buffer = {mac: [] for mac in self.anchors_mac}
        self.is_collecting = False
        self.collection_done = threading.Event()
        self.data_queue = queue.Queue()

        # 标定结果存储
        self.anchors_calibrated = self.anchors_init.copy()  # 初始化为初始值
        self.biases_calibrated = np.zeros(len(self.anchors_mac))

    # ---------------- 串口 / 设备交互（保持你的逻辑） ----------------
    def connect_serial(self):
        """连接串口（保持原逻辑）"""
        try:
            self.ser = serial.Serial(self.serial_port, self.baudrate, timeout=1)
            print(f"串口 {self.serial_port} 连接成功")
            return True
        except Exception as e:
            print(f"串口连接失败: {e}")
            return False

    def close_serial(self):
        if self.ser and self.ser.is_open:
            try:
                self.ser.close()
                print("串口已关闭")
            except Exception as e:
                print(f"关闭串口失败: {e}")

    def request_distance(self, mac):
        """向标签请求 UWB 距离（保持你原有的命令格式）
        返回 单次测距值（float，单位：m）或 None。
        注意：解析部分请和你设备实际返回字符串格式对齐。
        """
        if not self.ser or not self.ser.is_open:
            return None
        try:
            cmd = f"$mac,{mac},10\r\n"
            self.ser.write(cmd.encode())
            # 根据设备响应时间可能需要调整 sleep
            time.sleep(0.08)
            response = self.ser.readline().decode(errors='ignore').strip()
            # 你原示例中解析格式："mc,d4cf8914d31a,5,2.46m"
            # 如果实际格式不同，请按实际修改下方解析逻辑
            if not response:
                return None
            parts = response.split(',')
            if len(parts) >= 4 and parts[0] == 'mc' and parts[1] == mac:
                # 去掉末尾的 'm' 或非数字字符
                raw = parts[3]
                # 提取第一个能解析为 float 的子串
                num_str = ''
                for ch in raw:
                    if (ch.isdigit() or ch == '.' or ch == '-' or ch == '+'):
                        num_str += ch
                    else:
                        break
                try:
                    return float(num_str)
                except Exception:
                    return None
            # 另一个可能格式："d41b9a245222,3.52"
            if len(parts) >= 2 and parts[0] == mac:
                try:
                    # second part may have 'm' suffix
                    s = parts[1]
                    # remove non-numeric suffix
                    s2 = ''.join([c for c in s if (c.isdigit() or c in '.-+')])
                    return float(s2)
                except Exception:
                    return None
            # 若未匹配已知格式，打印调试信息（可注释掉）
            # print(f"未解析串口响应: '{response}'")
            return None
        except Exception as e:
            print(f"测距错误: {e}")
            return None

    def get_car_position(self):
        """通过 NavigationAPIClient 获取小车位姿，Z 固定为 1.29"""
        try:
            response = self.api_client.get_localization()
            if response and response.get('code') == 0:
                loc_info = response.get('data', {}).get('localization_info', {})
                if loc_info and 'position' in loc_info:
                    position = loc_info['position']
                    # 确保 position 至少包含 [x,y]
                    if isinstance(position, (list, tuple)) and len(position) >= 2:
                        return np.array([float(position[0]), float(position[1]), 0.56])
            return None
        except Exception as e:
            print(f"获取位置失败: {e}")
            return None

    def collect_calibration_point(self, num_samples=20):
        """
        在当前位置反复采样若干次，返回 (avg_pos, avg_distances)
        avg_distances 对应 anchors_mac 的顺序，若某锚无有效数据返回 np.nan
        """
        positions = []
        distances = {mac: [] for mac in self.anchors_mac}
        print("\n开始采集数据（每次采样会尝试从导航 API 和串口读取测距）...")

        for i in range(num_samples):
            print(f"  采样 {i+1}/{num_samples} ...")
            pos = self.get_car_position()
            if pos is not None:
                positions.append(pos)
                print(f"    位置: x={pos[0]:.4f}, y={pos[1]:.4f}, z={pos[2]:.4f}")
            else:
                print("    未能获取小车位置（跳过该次位置）")

            # 读取每个锚点距离
            for mac in self.anchors_mac:
                # 尝试多次读到有效值（小幅重试）
                dist = None
                for trial in range(2):
                    # 清空输入缓冲以减少残留
                    try:
                        if self.ser:
                            self.ser.reset_input_buffer()
                    except Exception:
                        pass
                    d = self.request_distance(mac)
                    if d is not None and not math.isnan(d):
                        dist = d
                        break
                    time.sleep(0.05)
                if dist is not None:
                    distances[mac].append(dist)
                    print(f"    锚 {mac} 距离: {dist:.4f} m")
                else:
                    print(f"    锚 {mac} 距离获取失败")
            # 稍作间隔
            time.sleep(0.2)

        if len(positions) == 0:
            print("没有有效位置样本，采集失败")
            return None, None

        # 位置取中位/平均（均可，使用平均）
        avg_pos = np.mean(positions, axis=0)
        print(f"平均位置: x={avg_pos[0]:.4f}, y={avg_pos[1]:.4f}, z={avg_pos[2]:.4f}")

        # 距离取中位数以降低异常值影响
        avg_distances = []
        for mac in self.anchors_mac:
            vals = [v for v in distances[mac] if v is not None]
            if vals:
                med = statistics.median(vals)
                avg_distances.append(med)
                print(f"  锚 {mac} 中位距离: {med:.4f} m (基于 {len(vals)} 次有效采样)")
            else:
                avg_distances.append(np.nan)
                print(f"  锚 {mac} 无有效采样")

        return avg_pos, avg_distances

    # ---------------- 优化核心（双阶段） ----------------
    @staticmethod
    def _linear_init_for_anchor(poses, d_i, min_required=4):
        """
        单锚线性初值（平方差差分法）
        poses: (M,3), d_i: (M,), 返回 (3,) 或 None（样本不足）
        """
        idx = np.where(np.isfinite(d_i))[0]
        if len(idx) < min_required:
            return None
        r = idx[0]
        p_r = poses[r]
        d_r = d_i[r]
        A_rows = []
        b_rows = []
        for k in idx:
            if k == r:
                continue
            p_k = poses[k]
            d_k = d_i[k]
            A_rows.append(2.0 * (p_k - p_r))
            rhs = np.dot(p_k, p_k) - np.dot(p_r, p_r) - (d_k**2 - d_r**2)
            b_rows.append(rhs)
        A = np.vstack(A_rows)
        b = np.array(b_rows)
        try:
            sol, *_ = np.linalg.lstsq(A, b, rcond=None)
            return sol
        except Exception:
            return None

    def calibrate_anchors_with_init(self,
                                    poses, ranges, anchors_init,
                                    estimate_bias=True,
                                    huber_delta=0.5,
                                    lam=0.1,
                                    bias_nonneg=True,
                                    irls_outer=5,
                                    force_fixed_z=False):
        """
        双阶段标定（支持固定高度 z）
        - 若 force_fixed_z True 则强制固定 anchors_init 中的 z；
        否则脚本会自动检测 poses 的几何秩：若 < 3 则自动固定 z。
        输入:
            poses: (M,3)
            ranges: (N,M)
            anchors_init: (>=N,3)
        返回:
            anchors_hat: (N,3), biases_hat: (N,), result: scipy result
        """
        import math

        poses = np.asarray(poses, dtype=float)
        ranges = np.asarray(ranges, dtype=float)
        anchors_init = np.asarray(anchors_init, dtype=float)

        if poses.ndim != 2 or poses.shape[1] != 3:
            raise ValueError("poses 必须为 (M,3)")
        if ranges.ndim != 2:
            raise ValueError("ranges 必须为 (N,M)")
        N, M = ranges.shape
        if anchors_init.shape[0] < N:
            raise ValueError("anchors_init 必须至少包含 N 个锚点的初始值")
        if M != poses.shape[0]:
            raise ValueError("ranges 的第二维必须与 poses 第一维一致 (测点数 M)")

        # --- 检测是否需要固定 z（共面退化） ---
        fixed_z_mode = force_fixed_z
        if not force_fixed_z:
            # 检查 poses 的秩（去均值后）
            U, S, Vt = np.linalg.svd(poses - poses.mean(axis=0))
            rank = np.sum(S > 1e-8)
            if rank < 3:
                fixed_z_mode = True
        print(f"fixed_z_mode = {fixed_z_mode}")

        # --- 阶段一：线性初值（只用于 x,y 估计或完整3D估计） ---
        # 我们仍尝试对每个锚进行线性初始化（若固定 z：只解 x,y）
        anchors0 = np.zeros((N, 3), dtype=float)
        biases0 = np.zeros(N, dtype=float)
        for i in range(N):
            d_i = ranges[i, :]
            # 如果固定 z，则以 anchors_init z 为真值，做 2D 线性拟合（在 xy 平面上）
            if fixed_z_mode:
                # 构造 A * [x;y] = b using (d^2 - (z - z0)^2) and xy terms
                z0 = anchors_init[i, 2]
                valid_idx = np.where(np.isfinite(d_i))[0]
                if len(valid_idx) >= 3:
                    A_rows = []
                    b_rows = []
                    # use first valid as ref
                    r = valid_idx[0]
                    pr = poses[r][:2]
                    dr = d_i[r]
                    for k in valid_idx:
                        if k == r:
                            continue
                        pk = poses[k][:2]
                        dk = d_i[k]
                        A_rows.append(2.0 * (pk - pr))
                        rhs = (pk[0]**2 + pk[1]**2) - (pr[0]**2 + pr[1]**2) - (dk**2 - dr**2) + (dk**2 - dr**2) * 0.0
                        # subtract z-terms: (z0 - pz)^2 differences handled by RHS below
                        rhs = (pk[0]**2 + pk[1]**2 + (poses[k][2]-z0)**2) - (pr[0]**2 + pr[1]**2 + (poses[r][2]-z0)**2) - (dk**2 - dr**2)
                        b_rows.append(rhs)
                    A = np.vstack(A_rows)
                    b = np.array(b_rows)
                    try:
                        sol_xy, *_ = np.linalg.lstsq(A, b, rcond=None)
                        anchors0[i, 0:2] = sol_xy[0:2]
                        anchors0[i, 2] = z0
                    except Exception:
                        anchors0[i] = anchors_init[i]
                else:
                    anchors0[i] = anchors_init[i]
            else:
                # 原 3D 线性初值（你已有的实现可替换进来）
                ai = self._linear_init_for_anchor(poses, d_i, min_required=4)
                if ai is None:
                    anchors0[i] = anchors_init[i]
                else:
                    anchors0[i] = ai
            # 初始偏置：用中位残差（若有观测）
            valid_idx = np.where(np.isfinite(d_i))[0]
            if len(valid_idx) > 0:
                pred = np.linalg.norm(poses[valid_idx] - anchors0[i], axis=1)
                biases0[i] = np.median(d_i[valid_idx] - pred)
            else:
                biases0[i] = 0.0
            # 如需 bias 非负则初步截断（防止 bounds 越界）
            if bias_nonneg:
                biases0[i] = max(biases0[i], 0.0)

        # 有效测量索引
        valid_measurements = [(i, j, ranges[i,j]) for i in range(N) for j in range(M) if np.isfinite(ranges[i,j])]
        if len(valid_measurements) == 0:
            raise ValueError("没有有效测量，无法标定")

        # --- 构造参数向量 --- 
        # 如果 fixed_z_mode：参数为 [x1,y1, x2,y2, ..., b1,b2,...] 长度 = 2N + N = 3N
        # 否则：参数为 [x1,y1,z1, ... , b1,...] 长度 = 3N + N = 4N
        if fixed_z_mode:
            # x layout: [x0,y0, x1,y1, ..., b0,b1,...]
            x0 = np.hstack([anchors0[:,0:2].ravel(), biases0])
        else:
            x0 = np.hstack([anchors0.ravel(), biases0])

        x_current = x0.copy()
        # ensure initial guess respects bias_nonneg
        if bias_nonneg:
            if fixed_z_mode:
                x_current[2*N:3*N] = np.maximum(x_current[2*N:3*N], 0.0)
            else:
                x_current[3*N:4*N] = np.maximum(x_current[3*N:4*N], 0.0)

        # 外层 IRLS（L1 近似）
        eps = 1e-8
        last_result = None
        for outer in range(max(1, irls_outer if lam > 0 else 1)):
            # compute w_b from current biases
            if fixed_z_mode:
                curr_bias = x_current[2*N:3*N]
            else:
                curr_bias = x_current[3*N:4*N]
            w_b = 1.0 / (np.abs(curr_bias) + eps)
            if np.all(np.isfinite(w_b)) and np.mean(w_b) > 0:
                w_b = w_b / np.mean(w_b)

            def residuals_var(x):
                res = []
                if fixed_z_mode:
                    anchors_xy = x[:2*N].reshape(N,2)
                    biases = x[2*N:3*N] if estimate_bias else np.zeros(N)
                    for (i,j,dmeas) in valid_measurements:
                        xi, yi = anchors_xy[i]
                        zi = anchors_init[i,2]  # fixed z
                        pj = poses[j]
                        pred = math.hypot(math.hypot(xi - pj[0], yi - pj[1]), zi - pj[2])
                        res.append(dmeas - pred - biases[i])
                    if estimate_bias and lam > 0:
                        for i in range(N):
                            res.append(math.sqrt(lam) * math.sqrt(w_b[i]) * biases[i])
                else:
                    anchors = x[:3*N].reshape(N,3)
                    biases = x[3*N:4*N] if estimate_bias else np.zeros(N)
                    for (i,j,dmeas) in valid_measurements:
                        ai = anchors[i]
                        pj = poses[j]
                        pred = np.linalg.norm(ai - pj)
                        res.append(dmeas - pred - biases[i])
                    if estimate_bias and lam > 0:
                        for i in range(N):
                            res.append(math.sqrt(lam) * math.sqrt(w_b[i]) * biases[i])
                return np.array(res, dtype=float)

            # bounds
            lower = np.full_like(x_current, -np.inf)
            upper = np.full_like(x_current, np.inf)
            if estimate_bias and bias_nonneg:
                if fixed_z_mode:
                    lower[2*N:3*N] = 0.0
                else:
                    lower[3*N:4*N] = 0.0

            # ensure x_current inside bounds
            if estimate_bias and bias_nonneg:
                if fixed_z_mode:
                    x_current[2*N:3*N] = np.maximum(x_current[2*N:3*N], 0.0)
                else:
                    x_current[3*N:4*N] = np.maximum(x_current[3*N:4*N], 0.0)

            result = least_squares(
                residuals_var,
                x_current,
                jac='2-point',
                bounds=(lower, upper),
                method='trf',
                loss='huber',
                f_scale=huber_delta,
                max_nfev=2000
            )

            x_new = result.x
            delta = np.linalg.norm(x_new - x_current)
            print(f"[IRLS {outer+1}] cost={result.cost:.6g}, delta={delta:.6g}")
            x_current = x_new
            last_result = result
            if delta < 1e-6:
                break

        # extract result
        if fixed_z_mode:
            anchors_hat = np.zeros((N,3), dtype=float)
            anchors_hat[:,0:2] = x_current[:2*N].reshape(N,2)
            anchors_hat[:,2] = anchors_init[:,2]
            biases_hat = x_current[2*N:3*N] if estimate_bias else np.zeros(N)
        else:
            anchors_hat = x_current[:3*N].reshape(N,3)
            biases_hat = x_current[3*N:4*N] if estimate_bias else np.zeros(N)

        return anchors_hat, biases_hat, last_result


    # ---------------- 高层采集 + 标定流程（保持与原脚本接口一致） ----------------
    def calibrate_anchors(self, estimate_bias=True, huber_delta=0.5, lam=0.1):
        """
        高层流程：人工确认 -> 采集多点 -> 调用 calibrate_anchors_with_init -> 保存结果
        保持与你原脚本交互一致
        """
        dataset = []
        if len(self.anchors_init) != len(self.anchors_mac):
            print(f"警告: anchors_init 长度 ({len(self.anchors_init)}) 与 anchors_mac ({len(self.anchors_mac)}) 不匹配")
            # 仍然继续，但 calibrate_anchors_with_init 会校验并出错或回退

        try:
            while True:
                input("\n移动小车至目标位置，按 Enter 开始采集...")
                print("开始采集，请保持小车静止...")
                pos, distances = self.collect_calibration_point(num_samples=20)
                if pos is None or distances is None:
                    print("采集失败，重试或检查设备")
                    if input("是否继续采集? (y/n): ").lower() != 'y':
                        break
                    else:
                        continue
                # 打印并存储
                print("\n本次采集结果：")
                print(f"  位置: x={pos[0]:.3f}, y={pos[1]:.3f}, z={pos[2]:.3f}")
                for mac, dist in zip(self.anchors_mac, distances):
                    print(f"  {mac} -> {dist:.3f} m")
                dataset.append((pos, distances))
                if input("\n继续采集新的位置? (y/n): ").lower() != 'y':
                    break
        except KeyboardInterrupt:
            print("\n采集被中断")

        if not dataset:
            print("未采集到任何数据，退出")
            return

        # 转换为矩阵
        poses = np.array([d[0] for d in dataset])          # (M,3)
        ranges = np.array([d[1] for d in dataset]).T       # (N,M)

        # 调用双阶段标定实现
        print("\n开始执行双阶段标定（线性初值 + 非线性精化）...")
        anchors_hat, biases_hat, result = self.calibrate_anchors_with_init(
            poses, ranges, self.anchors_init,
            estimate_bias=estimate_bias,
            huber_delta=huber_delta,
            lam=lam,
            bias_nonneg=True,
            irls_outer=5,
            force_fixed_z=True
        )

        # 保存标定结果到类属性
        self.anchors_calibrated = anchors_hat.copy()
        self.biases_calibrated = biases_hat.copy()

        # 输出并保存
        print("\n===== 标定结果 =====")
        for mac, init, hat, b in zip(self.anchors_mac, self.anchors_init, anchors_hat, biases_hat):
            print(f"锚 {mac}")
            print(f"  初始: {init}")
            print(f"  标定: {hat}")
            print(f"  偏置: {b:.3f} m")
        np.savez("anchors_calibrated.npz",
                 anchors=anchors_hat, biases=biases_hat, macs=self.anchors_mac,
                 poses=poses, ranges=ranges)
        print("结果已保存: anchors_calibrated.npz")

        # 可视化（若可用）
        if HAS_MPL:
            try:
                self.plot_results(poses, ranges, self.anchors_init, anchors_hat)
            except Exception as e:
                print(f"可视化失败: {e}")

    def plot_results(self, poses, ranges, anchors_init, anchors_hat):
        """3D 可视化：采样点、初始锚点、标定锚点"""
        if not HAS_MPL:
            print("未安装 matplotlib，跳过可视化")
            return
        anchors_init = np.asarray(anchors_init)
        anchors_hat = np.asarray(anchors_hat)
        fig = plt.figure(figsize=(9,7))
        ax = fig.add_subplot(111, projection='3d')
        # 采样点
        ax.scatter(poses[:,0], poses[:,1], poses[:,2], c='blue', marker='o', label='sample poses')
        # 初始锚点
        ax.scatter(anchors_init[:,0], anchors_init[:,1], anchors_init[:,2], c='orange', marker='^', s=80, label='anchors init')
        # 标定锚点
        ax.scatter(anchors_hat[:,0], anchors_hat[:,1], anchors_hat[:,2], c='red', marker='x', s=80, label='anchors calibrated')
        # 连接线
        for i in range(anchors_init.shape[0]):
            ax.plot([anchors_init[i,0], anchors_hat[i,0]],
                    [anchors_init[i,1], anchors_hat[i,1]],
                    [anchors_init[i,2], anchors_hat[i,2]],
                    c='gray', linestyle='--', linewidth=1)
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
        ax.legend()
        ax.set_title('UWB Anchor Calibration Results')
        plt.show()



    def localize_tag_irls(self,anchors, distances, z_fixed=1.29,
                        anchor_weights=None, max_iter=10, tol=1e-4):
        """
        IRLS + 锚点权重
        """
        N = len(anchors)
        xy0 = np.mean(anchors[:, :2], axis=0)
        est = np.array([xy0[0], xy0[1], z_fixed], dtype=float)

        if anchor_weights is None:
            anchor_weights = np.ones(N)

        weights = np.ones(N)

        for _ in range(max_iter):
            res, J, w_anchor = [], [], []
            for i in range(N):
                if np.isnan(distances[i]):
                    continue
                dx = est[0] - anchors[i, 0]
                dy = est[1] - anchors[i, 1]
                dz = est[2] - anchors[i, 2]
                pred = np.sqrt(dx**2 + dy**2 + dz**2)
                r = distances[i] - pred
                res.append(r)
                w_anchor.append(anchor_weights[i])  # 记录这个锚点的权重
                if pred > 1e-6:
                    J.append([dx/pred, dy/pred])
                else:
                    J.append([0, 0])

            res = np.array(res)
            J = np.array(J)
            w_anchor = np.array(w_anchor)

            if len(res) < 2:
                break

            # IRLS 残差权重
            sigma = np.std(res) if np.std(res) > 1e-6 else 1.0
            w_res = 1.0 / np.maximum(np.abs(res) / sigma, 1.0)

            # 结合锚点全局权重
            w_total = w_res * w_anchor
            W = np.diag(w_total)

            try:
                delta = np.linalg.inv(J.T @ W @ J) @ (J.T @ W @ res)
            except np.linalg.LinAlgError:
                break

            est[0:2] += delta
            if np.linalg.norm(delta) < tol:
                break

            weights = w_total

        return est, weights

    def run_localization_test(self, z_fixed=1.29, num_samples=20, method="tukey", delta=1.0):
        """
        定位测试流程:
        1. 输入确认键开始采集
        2. 每个锚点采集 num_samples 次
        3. 去掉异常值 (IQR)
        4. 用 IRLS (Tukey/Huber) 迭代定位
        """
        if not self.ser or not self.ser.is_open:
            print("串口未连接，无法测距")
            return

        while True:
            cmd = input("\n定位 (q退出): ")
            if cmd.lower() == "q":
                break

            print("开始测距...")

            anchors_valid = []
            distances_valid = []

            # 使用标定后的锚点坐标，如果没有则使用初始值
            anchors_to_use = self.anchors_calibrated if self.anchors_calibrated is not None else self.anchors_init
            for mac, anchor in zip(self.anchors_mac, anchors_to_use):
                samples = []
                for _ in range(num_samples):
                    self.ser.reset_input_buffer()
                    dist = self.request_distance(mac)
                    if dist is not None:
                        samples.append(dist)
                    time.sleep(0.05)

                if samples:
                    # 去掉异常值 (IQR 方法)
                    q1, q3 = np.percentile(samples, [25, 75])
                    iqr = q3 - q1
                    lower, upper = q1 - 1.5 * iqr, q3 + 1.5 * iqr
                    clean = [s for s in samples if lower <= s <= upper]

                    if clean:
                        d_final = np.median(clean)
                    else:
                        d_final = np.median(samples)

                    distances_valid.append(d_final)
                    anchors_valid.append(anchor)
                    print(f"锚点 {mac}: {d_final:.3f} m (clean {len(clean)}/{len(samples)})")
                else:
                    print(f"锚点 {mac}: 无效")

            if len(anchors_valid) < 3:
                print("有效锚点不足，无法定位")
                continue

            anchors_valid = np.array(anchors_valid)
            distances_valid = np.array(distances_valid)

            # === 鲁棒定位 ===
            # 获取对应的偏置
            biases_to_use = None
            if self.biases_calibrated is not None:
                # 找到有效锚点对应的偏置
                valid_indices = []
                for anchor in anchors_valid:
                    # 找到这个锚点在原始列表中的索引
                    for i, orig_anchor in enumerate(anchors_to_use):
                        if np.allclose(anchor, orig_anchor, atol=1e-6):
                            valid_indices.append(i)
                            break
                if len(valid_indices) == len(anchors_valid):
                    biases_to_use = self.biases_calibrated[valid_indices]

            est, weights, n_iter = self.robust_localize(
                    anchors_valid, distances_valid,
                    z_fixed=0.56,
                    method="huber",   # 或 "tukey"
                    delta=2.0,
                    k_nearest=3,      # 只用最近 3 个锚点
                    biases=biases_to_use
                )

            print(f"\n=== 定位结果 ===")
            print(f"X={est[0]:.3f}, Y={est[1]:.3f}, Z={est[2]:.3f}")
            print(f"收敛迭代次数: {n_iter}")
            print("最终权重:", ["{:.2f}".format(w) for w in weights])

    # def run_localization_test(self, z_fixed=1.29):
    #     """
    #     定位测试流程：
    #     - 等待用户输入确认键
    #     - 读取所有锚点的距离
    #     - 用IRLS算法计算盲点位置
    #     """
    #     from math import isnan
        
    #     while True:
    #         cmd = input("\n按 Enter 进行一次定位 (q退出): ")
    #         if cmd.lower() == 'q':
    #             print("退出定位测试")
    #             break

    #         distances = []
    #         print("开始测距...")
    #         for mac in self.anchors_mac:
    #             self.ser.reset_input_buffer()
    #             dist = self.request_distance(mac)
    #             if dist is not None and not isnan(dist):
    #                 print(f"锚点 {mac}: {dist:.3f} m")
    #                 distances.append(dist)
    #             else:
    #                 print(f"锚点 {mac}: 无效")
    #                 distances.append(np.nan)

    #         # 检查有效锚点数量
    #         valid_idx = [i for i, d in enumerate(distances) if not np.isnan(d)]
    #         if len(valid_idx) < 3:
    #             print("有效锚点不足，无法定位")
    #             continue

    #         # 用有效锚点计算定位
    #         anchors_valid = self.anchors_calibrated[valid_idx]
    #         distances_valid = np.array([distances[i] for i in valid_idx])

    #         est, weights = self.localize_tag_irls(anchors_valid, distances_valid, z_fixed=z_fixed)
    #         print(f"\n=== 定位结果 ===")
    #         print(f"X={est[0]:.3f}, Y={est[1]:.3f}, Z={est[2]:.3f}")
    #         print("权重:", ["{:.2f}".format(w) for w in weights])


# ---------------- 主函数 ----------------
# def main():
#     SERVER_URL = "http://192.168.3.6:10000"   # 请改为实际地址
#     SERIAL_PORT = "/dev/ttyTHS1"             # 请改为实际串口

#     calibrator = UWBCalibration(SERVER_URL, SERIAL_PORT)
#     if not calibrator.connect_serial():
#         print("无法连接串口，退出")
#         return
#     try:
#         calibrator.calibrate_anchors(estimate_bias=True, huber_delta=0.5, lam=0.1)
#     finally:
#         calibrator.close_serial()



    # def robust_localize(self, anchors, distances, z_fixed=1.29,
    #                     method="huber", delta=1.0, max_iter=20, tol=1e-4):
    #     """
    #     鲁棒IRLS定位 (Huber/Tukey)
    #     ----------
    #     anchors : ndarray (N,3) 锚点坐标
    #     distances : ndarray (N,) 观测距离
    #     z_fixed : float 盲点高度
    #     method : str "huber" 或 "tukey"
    #     delta : float 截断参数
    #     max_iter : int 最大迭代次数
    #     tol : float 收敛阈值
    #     返回:
    #     est : ndarray(3,) 估计位置
    #     weights : ndarray(N,) 每个锚点最终权重
    #     n_iter : int 收敛迭代次数
    #     """
    #     N = len(anchors)
    #     # 初始位置
    #     xy0 = np.mean(anchors[:, :2], axis=0)
    #     est = np.array([xy0[0], xy0[1], z_fixed], dtype=float)
    #     weights = np.ones(N)

    #     for it in range(max_iter):
    #         res, J = [], []
    #         for i in range(N):
    #             dx = est[0] - anchors[i, 0]
    #             dy = est[1] - anchors[i, 1]
    #             dz = est[2] - anchors[i, 2]
    #             pred = np.sqrt(dx**2 + dy**2 + dz**2)
    #             r = distances[i] - pred
    #             res.append(r)
    #             if pred > 1e-6:
    #                 J.append([dx/pred, dy/pred])
    #             else:
    #                 J.append([0, 0])
    #         res = np.array(res)
    #         J = np.array(J)

    #         if len(res) < 2:
    #             break

    #         # 更新权重
    #         if method == "huber":
    #             weights = np.where(np.abs(res) <= delta, 1.0, delta / (np.abs(res) + 1e-6))
    #         elif method == "tukey":
    #             w = np.zeros_like(res)
    #             mask = np.abs(res) < delta
    #             r_scaled = res[mask] / delta
    #             w[mask] = (1 - r_scaled**2)**2
    #             weights = w
    #         else:
    #             raise ValueError("未知权重方法")

    #         W = np.diag(weights)

    #         # 更新估计
    #         try:
    #             delta_xy = np.linalg.inv(J.T @ W @ J) @ (J.T @ W @ res)
    #         except np.linalg.LinAlgError:
    #             break

    #         est[0:2] += delta_xy

    #         if np.linalg.norm(delta_xy) < tol:
    #             return est, weights, it+1

    #     return est, weights, max_iter


    def robust_localize(self, anchors, distances, z_fixed=0.56,
                        method="huber", delta=2.0, max_iter=20, tol=1e-4, k_nearest=4, biases=None,
                        outlier_threshold=3.0):
        """
        鲁棒定位 (IRLS) - 改进版本
        anchors: (N,3) 锚点坐标
        distances: (N,) 距离
        z_fixed: 固定高度
        method: "huber" 或 "tukey"
        delta: 权重更新阈值
        k_nearest: 选取最近的 k 个锚点进行优化
        biases: (N,) 可选的距离偏置补偿
        outlier_threshold: 异常值检测阈值（标准差倍数）
        """

        # 0. 异常值检测和预处理
        print(f"原始数据: 锚点数={len(anchors)}, 距离={distances}")

        # 检测明显的异常测距值
        valid_mask = np.ones(len(distances), dtype=bool)

        # 基于距离范围的粗筛选（假设合理测距范围0.1-50m）
        range_mask = (distances >= 0.1) & (distances <= 50.0)
        if not np.all(range_mask):
            print(f"⚠️ 发现超出合理范围的距离: {distances[~range_mask]}")
            valid_mask &= range_mask

        # 基于锚点间距离的一致性检查
        for i in range(len(anchors)):
            if not valid_mask[i]:
                continue
            min_anchor_dist = float('inf')
            for j in range(len(anchors)):
                if i != j:
                    anchor_dist = np.linalg.norm(anchors[i] - anchors[j])
                    min_anchor_dist = min(min_anchor_dist, float(anchor_dist))

            # 如果测距值远大于最近锚点间距离，可能有问题
            if distances[i] > min_anchor_dist * 2:
                print(f"⚠️ 锚点{i}测距值{distances[i]:.3f}m 可能异常（最近锚点间距{min_anchor_dist:.3f}m）")

        # 应用有效性掩码
        if np.sum(valid_mask) < 3:
            print(f"⚠️ 有效锚点不足3个，使用所有数据")
            valid_mask = np.ones(len(distances), dtype=bool)

        anchors = anchors[valid_mask]
        distances = distances[valid_mask]
        if biases is not None:
            biases = biases[valid_mask]

        print(f"预处理后: 锚点数={len(anchors)}, 距离={distances}")

        # 1. 只保留最近的 k 个锚点
        idx_sorted = np.argsort(distances)
        idx_used = idx_sorted[:min(k_nearest, len(idx_sorted))]
        anchors = anchors[idx_used]
        distances = distances[idx_used]

        # 应用偏置补偿
        if biases is not None:
            biases = biases[idx_used]
            distances = distances - biases  # 减去偏置
            print(f"应用偏置补偿: {biases}")
        else:
            biases = np.zeros(len(distances))

        N = len(anchors)
        if N < 3:
            raise ValueError("有效锚点不足 3 个，无法定位")

        # 2. 改进的初值估计
        pos_xy = self._get_robust_initial_position(anchors, distances, z_fixed)
        print(f"初始位置估计: [{pos_xy[0]:.3f}, {pos_xy[1]:.3f}]")

        # 权重初始化
        weights = np.ones(N)

        def compute_residuals(pos_xy):
            pos = np.array([pos_xy[0], pos_xy[1], z_fixed])
            pred = np.linalg.norm(anchors - pos, axis=1)
            return distances - pred

        # 记录收敛历史
        prev_cost = float('inf')
        it = 0  # 初始化迭代计数器

        # 3. IRLS 迭代
        for it in range(max_iter):
            pos = np.array([pos_xy[0], pos_xy[1], z_fixed])
            r = compute_residuals(pos_xy)

            # 计算当前代价函数值
            current_cost = np.sum(weights * r**2)

            # 更新权重
            if method == "huber":
                weights = np.where(np.abs(r) <= delta, 1.0, delta / (np.abs(r) + 1e-8))
            elif method == "tukey":
                mask = np.abs(r) < delta
                weights = np.zeros_like(r)
                weights[mask] = (1 - (r[mask] / delta) ** 2) ** 2
            else:
                raise ValueError("未知 method, 请选择 'huber' 或 'tukey'")

            # 构建加权最小二乘问题
            A = []
            b = []
            for i in range(N):
                # 计算当前位置到锚点的距离
                dx = pos[0] - anchors[i, 0]
                dy = pos[1] - anchors[i, 1]
                dz = pos[2] - anchors[i, 2]
                pred_dist = np.sqrt(dx**2 + dy**2 + dz**2)

                if pred_dist > 1e-6:  # 避免除零
                    # 雅可比矩阵：对 x, y 的偏导数
                    jx = dx / pred_dist
                    jy = dy / pred_dist

                    # 加权雅可比和残差
                    w_sqrt = np.sqrt(weights[i])
                    A.append([w_sqrt * jx, w_sqrt * jy])
                    b.append(w_sqrt * r[i])
                else:
                    # 距离太小时使用单位向量
                    A.append([np.sqrt(weights[i]), 0])
                    b.append(np.sqrt(weights[i]) * r[i])

            A = np.array(A)
            b = np.array(b)

            # 解正规方程
            try:
                delta_xy, _, _, _ = np.linalg.lstsq(A, b, rcond=None)

                # 步长控制：防止过大的更新导致发散
                max_step = 5.0  # 最大步长5米
                step_norm = np.linalg.norm(delta_xy)
                if step_norm > max_step:
                    delta_xy = delta_xy * (max_step / step_norm)
                    print(f"迭代 {it+1}: 限制步长从 {step_norm:.3f}m 到 {max_step}m")

                pos_xy_new = pos_xy + delta_xy
            except np.linalg.LinAlgError:
                print(f"迭代 {it+1}: 线性方程求解失败")
                break

            # 检查收敛条件：位置变化 AND 代价函数改善
            pos_change = np.linalg.norm(delta_xy)
            cost_change = abs(current_cost - prev_cost) / (prev_cost + 1e-8)

            print(f"迭代 {it+1}: 位置变化={pos_change:.6f}, 代价变化={cost_change:.6f}, RMS残差={np.sqrt(np.mean(r**2)):.4f}")

            # 更新位置
            pos_xy = pos_xy_new
            prev_cost = current_cost

            # 收敛判断：位置变化小且代价函数变化小
            if pos_change < tol and cost_change < tol:
                print(f"收敛于迭代 {it+1}")
                break
            elif it == max_iter - 1:
                print(f"达到最大迭代次数 {max_iter}")

        # 4. 最终估计
        est = np.array([pos_xy[0], pos_xy[1], z_fixed])
        final_residuals = compute_residuals(pos_xy)

        # 打印残差信息
        print("\n=== 残差分析 ===")
        for i in range(N):
            pred = np.linalg.norm(anchors[i] - est)
            # 如果有偏置，显示原始距离和补偿后距离
            if biases is not None and len(biases) > i:
                original_dist = distances[i] + biases[i]  # 恢复原始距离
                print(f"锚点{i}: 原始={original_dist:.3f}, 补偿后={distances[i]:.3f}, 预测={pred:.3f}, 残差={final_residuals[i]:.3f}, 权重={weights[i]:.2f}")
            else:
                print(f"锚点{i}: 实测={distances[i]:.3f}, 预测={pred:.3f}, 残差={final_residuals[i]:.3f}, 权重={weights[i]:.2f}")

        # 检查残差是否还很大
        rms_residual = np.sqrt(np.mean(final_residuals**2))
        print(f"RMS残差: {rms_residual:.4f}m")
        if rms_residual > 0.2:  # 如果RMS残差大于20cm
            print(f"⚠️ 警告: RMS残差 {rms_residual:.3f}m 仍然较大，可能需要:")
            print("  1. 检查锚点坐标是否准确")
            print("  2. 增加迭代次数或调整收敛参数")
            print("  3. 检查测距数据质量")
        else:
            print(f"✓ 残差在合理范围内 ({rms_residual:.3f}m)")

        return est, weights, it + 1

    def _get_robust_initial_position(self, anchors, distances, z_fixed):
        """
        获取鲁棒的初始位置估计
        尝试多种方法并选择最合理的结果
        """
        candidates = []

        # 方法1: 锚点中心
        center = np.mean(anchors[:, :2], axis=0)
        candidates.append(center)

        # 方法2: 加权中心（距离越近权重越大）
        weights = 1.0 / (distances + 0.1)  # 避免除零
        weights /= np.sum(weights)
        weighted_center = np.sum(anchors[:, :2] * weights.reshape(-1, 1), axis=0)
        candidates.append(weighted_center)

        # 方法3: 尝试线性最小二乘（如果有足够锚点）
        if len(anchors) >= 3:
            try:
                linear_pos = self._linear_least_squares_2d(anchors, distances, z_fixed)
                if np.all(np.isfinite(linear_pos)):
                    candidates.append(linear_pos)
            except:
                pass

        # 评估候选位置，选择残差最小的
        best_pos = center  # 默认值
        best_residual = float('inf')

        for candidate in candidates:
            pos_3d = np.array([candidate[0], candidate[1], z_fixed])
            pred_distances = np.linalg.norm(anchors - pos_3d, axis=1)
            residual = np.sqrt(np.mean((distances - pred_distances)**2))

            if residual < best_residual:
                best_residual = residual
                best_pos = candidate

        return best_pos

    def _linear_least_squares_2d(self, anchors, distances, z_fixed):
        """
        2D线性最小二乘求解（固定z）
        """
        N = len(anchors)
        if N < 3:
            raise ValueError("锚点数量不足")

        # 使用第一个锚点作为参考
        ref_anchor = anchors[0]
        ref_dist = distances[0]

        A = []
        b = []

        for i in range(1, N):
            anchor_i = anchors[i]
            dist_i = distances[i]

            # 构建线性方程
            coeff = 2 * (anchor_i[:2] - ref_anchor[:2])
            rhs = (np.dot(anchor_i, anchor_i) - np.dot(ref_anchor, ref_anchor) -
                   (dist_i**2 - ref_dist**2) +
                   (anchor_i[2] - z_fixed)**2 - (ref_anchor[2] - z_fixed)**2)

            A.append(coeff)
            b.append(rhs)

        A = np.array(A)
        b = np.array(b)

        # 求解
        xy_solution, _, _, _ = np.linalg.lstsq(A, b, rcond=None)

        return xy_solution

def main():
    SERVER_URL = "http://192.168.3.6:10000"   # 你的导航服务器
    SERIAL_PORT = "/dev/ttyTHS1"              # 串口端口
    
    calibrator = UWBCalibration(SERVER_URL, SERIAL_PORT)

    # 1. 尝试加载已有标定文件
    anchors_file = "anchors_calibrated.npz"
    use_calibrated = False
    try:
        data = np.load(anchors_file, allow_pickle=True)
        calibrator.anchors_calibrated = data["anchors"]
        calibrator.biases_calibrated = data["biases"]
        calibrator.anchors_mac = list(data["macs"])
        use_calibrated = True
        print(f"已加载标定文件 {anchors_file}")
    except FileNotFoundError:
        print("未找到标定文件，将使用 anchors_init 作为初始值")

    # 2. 选择流程
    print("\n请选择操作：")
    print("1. 开始标定（然后进入定位测试）")
    print("2. 直接定位（使用已有标定文件或 anchors_init）")
    choice = input("输入选项 (1/2): ").strip()

    if not calibrator.connect_serial():
        return
    
    try:
        if choice == "1":
            # 执行标定
            calibrator.calibrate_anchors()
            # 标定完成后再进入定位模式
            calibrator.run_localization_test(z_fixed=0.56)

        elif choice == "2":
            if not use_calibrated:
                print("⚠️ 警告: 没有标定文件，直接用 anchors_init 定位")
                calibrator.anchors_calibrated = calibrator.anchors_init
                calibrator.biases_calibrated = np.zeros(len(calibrator.anchors_init))
            #calibrator.run_localization_test(z_fixed=0.56)
            calibrator.run_localization_test(z_fixed=0.56, num_samples=20, method="huber", delta=2)


        else:
            print("无效选项，退出程序")

    finally:
        calibrator.close_serial()


if __name__ == "__main__":
    main()
