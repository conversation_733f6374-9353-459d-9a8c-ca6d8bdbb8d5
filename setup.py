from setuptools import setup

package_name = 'robot_status_publisher'

setup(
    name=package_name,
    version='0.0.1',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Your Name',
    maintainer_email='<EMAIL>',
    description='ROS2 package for publishing robot base status using API client',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'robot_status_publisher = robot_status_publisher.robot_status_publisher:main',
        ],
    },
)
