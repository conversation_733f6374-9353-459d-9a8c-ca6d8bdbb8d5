import serial
import time

# --- 用户配置 ---
# 请根据实际情况修改以下参数
SERIAL_PORT = '/dev/ttyTHS1'  # 串口设备名称
BAUDRATE = 115200                  # 波特率
# -----------------

def main():
    print("串口通信测试程序")
    
    # 打开串口
    try:
        ser = serial.Serial(SERIAL_PORT, BAUDRATE, timeout=10)
        print(f"已连接到串口 {SERIAL_PORT}，波特率 {BAUDRATE}")
    except serial.SerialException as e:
        print(f"无法打开串口 {SERIAL_PORT}: {e}")
        return

    try:
        # 清空串口缓冲区
        ser.reset_input_buffer()
        print("串口缓冲区已清空")
        
        # 发送简单的测试数据
        test_data = "$mac,d4cf8914d31a,10\r\n"
    
        ser.write(test_data.encode('utf-8'))
        print(f"已发送测试数据: {test_data.strip()}")
        
        print("等待接收数据 (10秒超时)...")
        
        # 等待接收数据
        start_time = time.time()
     
        timeout = 1
        
        while (time.time() - start_time) < timeout:
            # 从串口读取一行数据
            try:
                line = ser.readline()
            except Exception as e:
                print(f"读取串口数据时出错: {e}")
                break
            
            # 如果读取到数据，则进行处理
            if line:
                try:
                    # 将字节串解码为字符串，并去除首尾空白字符
                    data = line.decode('utf-8').strip()
                    if data:  # 只处理非空数据
                        print(f"接收到数据: {data}")
                except UnicodeDecodeError:
                    print("接收到无法解码的数据")
            
            # 短暂休眠以避免CPU占用过高
            time.sleep(0.001)
            
        print(f"耗时：{time.time()-start_time}")
        print("测试完成")
            
    except KeyboardInterrupt:
        # 捕获 Ctrl+C，用于优雅地退出程序
        print("\n程序已退出。")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭串口
        if 'ser' in locals() and ser.is_open:
            ser.close()
        print("串口已关闭。")


if __name__ == "__main__":
    main()


