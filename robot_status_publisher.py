#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import json
import time
from api_client import NavigationAPIClient


class RobotStatusPublisher(Node):
    """
    ROS2 node that publishes robot base status by calling the API client's get_localization method.
    """
    
    def __init__(self):
        super().__init__('robot_status_publisher')
        
        # Create publisher for robot_base_status topic
        self.publisher_ = self.create_publisher(String, 'robot_base_status', 10)
        
        # Initialize API client
        # You may need to adjust the server URL according to your setup
        self.api_client = NavigationAPIClient("http://localhost:10000")
        
        # Create timer that calls the callback function every 1 second
        self.timer = self.create_timer(1.0, self.timer_callback)
        
        self.get_logger().info('Robot Status Publisher has been started.')
    
    def timer_callback(self):
        """
        Timer callback function that gets localization data and publishes it.
        """
        try:
            # Call the get_localization method from api_client
            localization_data = self.api_client.get_localization()
            
            if localization_data and localization_data.get('code') == 0:
                # Extract localization info from the response
                localization_info = localization_data.get('data', {}).get('localization_info')
                
                if localization_info:
                    # Create the message content with robot_pose
                    # Assuming localization_info contains x, y, yaw coordinates
                    # You may need to adjust this based on the actual structure of localization_info
                    robot_pose = [
                        localization_info.get('x', 0.16751950979232788),
                        localization_info.get('y', 0.6707438826560974), 
                        localization_info.get('yaw', 38.200951997559166)
                    ]
                    
                    # Create the message content
                    message_content = {
                        "robot_pose": robot_pose
                    }
                    
                    # Create and publish the message
                    msg = String()
                    msg.data = json.dumps(message_content)
                    self.publisher_.publish(msg)
                    
                    self.get_logger().info(f'Published robot_base_status: {message_content}')
                else:
                    # If no localization info, publish default values
                    default_message = {
                        "robot_pose": [0.16751950979232788, 0.6707438826560974, 38.200951997559166]
                    }
                    msg = String()
                    msg.data = json.dumps(default_message)
                    self.publisher_.publish(msg)
                    
                    self.get_logger().warn('No localization info received, published default values')
            else:
                # If API call failed, publish default values
                default_message = {
                    "robot_pose": [0.16751950979232788, 0.6707438826560974, 38.200951997559166]
                }
                msg = String()
                msg.data = json.dumps(default_message)
                self.publisher_.publish(msg)
                
                error_code = localization_data.get('code') if localization_data else 'Unknown'
                self.get_logger().error(f'API call failed with code: {error_code}, published default values')
                
        except Exception as e:
            # Handle any exceptions and publish default values
            default_message = {
                "robot_pose": [0.16751950979232788, 0.6707438826560974, 38.200951997559166]
            }
            msg = String()
            msg.data = json.dumps(default_message)
            self.publisher_.publish(msg)
            
            self.get_logger().error(f'Exception occurred: {str(e)}, published default values')


def main(args=None):
    """
    Main function to initialize and run the ROS2 node.
    """
    rclpy.init(args=args)
    
    robot_status_publisher = RobotStatusPublisher()
    
    try:
        rclpy.spin(robot_status_publisher)
    except KeyboardInterrupt:
        pass
    finally:
        robot_status_publisher.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
