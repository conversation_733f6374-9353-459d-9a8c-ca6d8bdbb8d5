#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的UWB定位算法核心部分
不依赖串口等外部模块
"""

import numpy as np
import math

def robust_localize_improved(anchors, distances, z_fixed=0.56,
                    method="huber", delta=2.0, max_iter=20, tol=1e-4, k_nearest=4, biases=None):
    """
    改进的鲁棒定位算法 (IRLS)
    """
    
    # 1. 只保留最近的 k 个锚点
    idx_sorted = np.argsort(distances)
    idx_used = idx_sorted[:min(k_nearest, len(idx_sorted))]
    anchors = anchors[idx_used]
    distances = distances[idx_used]
    
    # 应用偏置补偿
    if biases is not None:
        biases = biases[idx_used]
        distances = distances - biases  # 减去偏置
        print(f"应用偏置补偿: {biases}")
    else:
        biases = np.zeros(len(distances))

    N = len(anchors)
    if N < 3:
        raise ValueError("有效锚点不足 3 个，无法定位")

    # 2. 初值：锚点中心点
    x0 = np.mean(anchors[:, :2], axis=0)
    pos_xy = np.array([x0[0], x0[1]])

    # 权重初始化
    weights = np.ones(N)

    def compute_residuals(pos_xy):
        pos = np.array([pos_xy[0], pos_xy[1], z_fixed])
        pred = np.linalg.norm(anchors - pos, axis=1)
        return distances - pred

    # 记录收敛历史
    prev_cost = float('inf')
    it = 0  # 初始化迭代计数器
    
    # 3. IRLS 迭代
    for it in range(max_iter):
        pos = np.array([pos_xy[0], pos_xy[1], z_fixed])
        r = compute_residuals(pos_xy)
        
        # 计算当前代价函数值
        current_cost = np.sum(weights * r**2)

        # 更新权重
        if method == "huber":
            weights = np.where(np.abs(r) <= delta, 1.0, delta / (np.abs(r) + 1e-8))
        elif method == "tukey":
            mask = np.abs(r) < delta
            weights = np.zeros_like(r)
            weights[mask] = (1 - (r[mask] / delta) ** 2) ** 2
        else:
            raise ValueError("未知 method, 请选择 'huber' 或 'tukey'")

        # 构建加权最小二乘问题
        A = []
        b = []
        for i in range(N):
            # 计算当前位置到锚点的距离
            dx = pos[0] - anchors[i, 0]
            dy = pos[1] - anchors[i, 1]
            dz = pos[2] - anchors[i, 2]
            pred_dist = np.sqrt(dx**2 + dy**2 + dz**2)
            
            if pred_dist > 1e-6:  # 避免除零
                # 雅可比矩阵：对 x, y 的偏导数
                jx = dx / pred_dist
                jy = dy / pred_dist
                
                # 加权雅可比和残差
                w_sqrt = np.sqrt(weights[i])
                A.append([w_sqrt * jx, w_sqrt * jy])
                b.append(w_sqrt * r[i])
            else:
                # 距离太小时使用单位向量
                A.append([np.sqrt(weights[i]), 0])
                b.append(np.sqrt(weights[i]) * r[i])
                
        A = np.array(A)
        b = np.array(b)

        # 解正规方程
        try:
            delta_xy, _, _, _ = np.linalg.lstsq(A, b, rcond=None)
            pos_xy_new = pos_xy + delta_xy
        except np.linalg.LinAlgError:
            print(f"迭代 {it+1}: 线性方程求解失败")
            break

        # 检查收敛条件：位置变化 AND 代价函数改善
        pos_change = np.linalg.norm(delta_xy)
        cost_change = abs(current_cost - prev_cost) / (prev_cost + 1e-8)
        
        print(f"迭代 {it+1}: 位置变化={pos_change:.6f}, 代价变化={cost_change:.6f}, RMS残差={np.sqrt(np.mean(r**2)):.4f}")
        
        # 更新位置
        pos_xy = pos_xy_new
        prev_cost = current_cost
        
        # 收敛判断：位置变化小且代价函数变化小
        if pos_change < tol and cost_change < tol:
            print(f"收敛于迭代 {it+1}")
            break
        elif it == max_iter - 1:
            print(f"达到最大迭代次数 {max_iter}")

    # 4. 最终估计
    est = np.array([pos_xy[0], pos_xy[1], z_fixed])
    final_residuals = compute_residuals(pos_xy)

    # 打印残差信息
    print("\n=== 残差分析 ===")
    for i in range(N):
        pred = np.linalg.norm(anchors[i] - est)
        # 如果有偏置，显示原始距离和补偿后距离
        if biases is not None and len(biases) > i:
            original_dist = distances[i] + biases[i]  # 恢复原始距离
            print(f"锚点{i}: 原始={original_dist:.3f}, 补偿后={distances[i]:.3f}, 预测={pred:.3f}, 残差={final_residuals[i]:.3f}, 权重={weights[i]:.2f}")
        else:
            print(f"锚点{i}: 实测={distances[i]:.3f}, 预测={pred:.3f}, 残差={final_residuals[i]:.3f}, 权重={weights[i]:.2f}")
    
    # 检查残差是否还很大
    rms_residual = np.sqrt(np.mean(final_residuals**2))
    print(f"RMS残差: {rms_residual:.4f}m")
    if rms_residual > 0.2:  # 如果RMS残差大于20cm
        print(f"⚠️ 警告: RMS残差 {rms_residual:.3f}m 仍然较大，可能需要:")
        print("  1. 检查锚点坐标是否准确")
        print("  2. 增加迭代次数或调整收敛参数")
        print("  3. 检查测距数据质量")
    else:
        print(f"✓ 残差在合理范围内 ({rms_residual:.3f}m)")

    return est, weights, it + 1

def test_with_your_data():
    """
    使用你提供的实际数据进行测试
    """
    print("=== 使用实际数据测试改进算法 ===\n")
    
    # 你的锚点坐标（使用初始值，实际应该用标定后的值）
    anchors = np.array([
        [-0.15, 2.19, 1.4],
        [-6.2474, -8.8274, 2.38], 
        [-3.2080, -3.1134, 2.68]
    ])
    
    # 你的测距数据
    distances = np.array([1.785, 3.750, 6.380])
    
    print("输入数据:")
    print(f"锚点坐标:\n{anchors}")
    print(f"测量距离: {distances}")
    print()
    
    # 测试改进算法
    try:
        est, weights, n_iter = robust_localize_improved(
            anchors, distances,
            z_fixed=0.56,
            method="huber",
            delta=2.0,
            max_iter=20,
            tol=1e-5,  # 稍微严格一点的收敛条件
            k_nearest=3
        )
        
        print(f"\n最终估计位置: [{est[0]:.3f}, {est[1]:.3f}, {est[2]:.3f}]")
        print(f"总迭代次数: {n_iter}")
        
    except Exception as e:
        print(f"算法执行失败: {e}")

if __name__ == "__main__":
    test_with_your_data()
    
    print("\n" + "="*60)
    print("主要改进点总结:")
    print("1. 修复雅可比矩阵计算 - 正确计算距离和偏导数")
    print("2. 改进收敛判断 - 同时考虑位置变化和代价函数变化")
    print("3. 添加偏置补偿 - 使用标定得到的距离偏置")
    print("4. 详细监控 - 每次迭代显示收敛信息")
    print("5. 残差评估 - 提供RMS残差和质量警告")
    print("6. 数值稳定性 - 处理除零和奇异矩阵情况")
