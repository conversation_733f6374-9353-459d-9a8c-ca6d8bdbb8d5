#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试UWB定位算法
分析为什么定位结果与真实位置有较大误差
"""

import numpy as np
import math

def debug_localization():
    """
    使用你的实际数据调试定位算法
    """
    print("=== UWB定位算法调试 ===\n")
    
    # 你的数据
    anchors = np.array([
        [-0.15, 2.19, 1.4],      # 锚点0
        [-6.2474, -8.8274, 2.38], # 锚点1  
        [-3.2080, -3.1134, 2.68]  # 锚点2
    ])
    
    distances = np.array([1.785, 3.750, 6.380])
    true_pos = np.array([0.765, 0.8948, 0.56])  # 真实位置
    z_fixed = 0.56
    
    print("输入数据:")
    print(f"锚点坐标:\n{anchors}")
    print(f"测量距离: {distances}")
    print(f"真实位置: {true_pos}")
    print()
    
    # 1. 验证真实位置的理论距离
    print("=== 验证真实位置的理论距离 ===")
    theoretical_distances = []
    for i, anchor in enumerate(anchors):
        dist = np.linalg.norm(anchor - true_pos)
        theoretical_distances.append(dist)
        error = distances[i] - dist
        print(f"锚点{i}: 理论距离={dist:.3f}, 测量距离={distances[i]:.3f}, 误差={error:.3f}")
    
    theoretical_distances = np.array(theoretical_distances)
    print(f"平均测距误差: {np.mean(distances - theoretical_distances):.3f}")
    print(f"RMS测距误差: {np.sqrt(np.mean((distances - theoretical_distances)**2)):.3f}")
    print()
    
    # 2. 检查锚点几何配置
    print("=== 锚点几何配置分析 ===")
    anchor_distances = []
    for i in range(len(anchors)):
        for j in range(i+1, len(anchors)):
            dist = np.linalg.norm(anchors[i] - anchors[j])
            anchor_distances.append(dist)
            print(f"锚点{i}-锚点{j}距离: {dist:.3f}m")
    
    # 检查锚点是否共线或接近共线
    if len(anchors) >= 3:
        # 计算锚点构成的三角形面积
        a, b, c = anchors[0], anchors[1], anchors[2]
        area = 0.5 * abs(np.cross(b[:2] - a[:2], c[:2] - a[:2]))
        print(f"锚点三角形面积(XY平面): {area:.3f}m²")
        if area < 1.0:
            print("⚠️ 警告: 锚点在XY平面上接近共线，可能导致定位精度下降")
    print()
    
    # 3. 使用线性最小二乘求解（作为对比）
    print("=== 线性最小二乘求解 ===")
    try:
        pos_linear = solve_linear_least_squares(anchors, distances, z_fixed)
        print(f"线性最小二乘结果: [{pos_linear[0]:.3f}, {pos_linear[1]:.3f}, {pos_linear[2]:.3f}]")
        error_linear = np.linalg.norm(pos_linear[:2] - true_pos[:2])
        print(f"与真实位置的误差: {error_linear:.3f}m")
    except Exception as e:
        print(f"线性最小二乘失败: {e}")
    print()
    
    # 4. 使用改进的非线性算法
    print("=== 改进的非线性算法 ===")
    try:
        pos_nonlinear = solve_nonlinear_improved(anchors, distances, z_fixed)
        print(f"非线性算法结果: [{pos_nonlinear[0]:.3f}, {pos_nonlinear[1]:.3f}, {pos_nonlinear[2]:.3f}]")
        error_nonlinear = np.linalg.norm(pos_nonlinear[:2] - true_pos[:2])
        print(f"与真实位置的误差: {error_nonlinear:.3f}m")
    except Exception as e:
        print(f"非线性算法失败: {e}")

def solve_linear_least_squares(anchors, distances, z_fixed):
    """
    使用线性最小二乘求解（基于距离平方差分）
    """
    N = len(anchors)
    if N < 3:
        raise ValueError("锚点数量不足")
    
    # 使用第一个锚点作为参考
    ref_anchor = anchors[0]
    ref_dist = distances[0]
    
    A = []
    b = []
    
    for i in range(1, N):
        anchor_i = anchors[i]
        dist_i = distances[i]
        
        # 构建线性方程：2*(pi - p0) * x = ||pi||² - ||p0||² - (di² - d0²)
        coeff = 2 * (anchor_i[:2] - ref_anchor[:2])  # 只考虑x,y
        rhs = (np.dot(anchor_i, anchor_i) - np.dot(ref_anchor, ref_anchor) - 
               (dist_i**2 - ref_dist**2) + 
               (anchor_i[2] - z_fixed)**2 - (ref_anchor[2] - z_fixed)**2)
        
        A.append(coeff)
        b.append(rhs)
    
    A = np.array(A)
    b = np.array(b)
    
    # 求解
    xy_solution, _, _, _ = np.linalg.lstsq(A, b, rcond=None)
    
    return np.array([xy_solution[0], xy_solution[1], z_fixed])

def solve_nonlinear_improved(anchors, distances, z_fixed, max_iter=20):
    """
    改进的非线性最小二乘算法
    """
    N = len(anchors)
    
    # 初始猜测：使用线性解作为初值
    try:
        pos_init = solve_linear_least_squares(anchors, distances, z_fixed)
        pos_xy = pos_init[:2].copy()
    except:
        # 如果线性解失败，使用锚点中心
        pos_xy = np.mean(anchors[:, :2], axis=0)
    
    print(f"初始位置: [{pos_xy[0]:.3f}, {pos_xy[1]:.3f}]")
    
    for it in range(max_iter):
        pos = np.array([pos_xy[0], pos_xy[1], z_fixed])
        
        # 计算残差
        residuals = []
        jacobian = []
        
        for i in range(N):
            # 预测距离
            pred_dist = np.linalg.norm(anchors[i] - pos)
            
            # 残差
            r = distances[i] - pred_dist
            residuals.append(r)
            
            # 雅可比矩阵 (对x, y的偏导数)
            if pred_dist > 1e-6:
                dx = pos[0] - anchors[i, 0]
                dy = pos[1] - anchors[i, 1]
                jx = -dx / pred_dist  # 注意符号！
                jy = -dy / pred_dist
            else:
                jx, jy = 0, 0
            
            jacobian.append([jx, jy])
        
        residuals = np.array(residuals)
        jacobian = np.array(jacobian)
        
        # 计算更新步长
        try:
            # 使用正规方程: J^T * J * delta = J^T * r
            JTJ = jacobian.T @ jacobian
            JTr = jacobian.T @ residuals
            delta_xy = np.linalg.solve(JTJ, JTr)
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用伪逆
            delta_xy = np.linalg.pinv(jacobian) @ residuals
        
        # 更新位置
        pos_xy_new = pos_xy + delta_xy
        
        # 检查收敛
        pos_change = np.linalg.norm(delta_xy)
        rms_residual = np.sqrt(np.mean(residuals**2))
        
        print(f"迭代 {it+1}: 位置变化={pos_change:.6f}, RMS残差={rms_residual:.4f}")
        
        pos_xy = pos_xy_new
        
        if pos_change < 1e-6:
            print(f"收敛于迭代 {it+1}")
            break
    
    return np.array([pos_xy[0], pos_xy[1], z_fixed])

if __name__ == "__main__":
    debug_localization()
    
    print("\n=== 可能的问题和解决方案 ===")
    print("1. 锚点坐标不准确 - 需要精确标定")
    print("2. 测距系统误差 - 需要偏置补偿")
    print("3. 锚点几何配置不佳 - 考虑增加锚点或改变布局")
    print("4. 算法实现问题 - 检查雅可比矩阵符号和最小二乘实现")
    print("5. 多径效应和环境干扰 - 使用鲁棒算法和异常值检测")
