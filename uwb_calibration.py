import serial
import time
import numpy as np
from scipy.optimize import least_squares
from api_client import NavigationAPIClient
import json
from collections import deque
import threading
import queue
import statistics

class UWBCalibration:
    def __init__(self, server_url, serial_port, baudrate=115200):
        """
        初始化UWB标定系统
        :param server_url: 导航服务器URL
        :param serial_port: UWB串口端口
        :param baudrate: 串口波特率
        """
        # 初始化API客户端
        self.api_client = NavigationAPIClient(server_url)
        
        # 串口配置
        self.serial_port = serial_port
        self.baudrate = baudrate
        self.ser = None
        
        # 锚点配置
        self.anchors_mac = [
            "d4cf8914d31a",
            # "d4da8324d322",
            # "d422685c5222",
            # "d41b9a245222",
        ]
        self.anchors_init = np.array([
            [-5.31, 10.664, 2.38],
            # [3.739, 2.7146, 2.785],
            # [-8.6106, 2.3646, 2.54],
            # [-0.9106, -1.3353, 1.47],
        ])
        
        # 数据缓冲区
        self.position_buffer = deque(maxlen=50)  # 存储位置数据
        self.distance_buffer = {mac: [] for mac in self.anchors_mac}  # 存储距离数据
        
        # 线程同步
        self.is_collecting = False
        self.collection_done = threading.Event()
        self.data_queue = queue.Queue()
        
    def connect_serial(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.serial_port, self.baudrate, timeout=1)
            print(f"串口 {self.serial_port} 连接成功")
            return True
        except Exception as e:
            print(f"串口连接失败: {e}")
            return False

    def close_serial(self):
        """关闭串口"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已关闭")

    def request_distance(self, mac):
        """请求UWB距离数据"""
        if not self.ser or not self.ser.is_open:
            return None
            
        try:
            # 发送测距指令
            cmd = f"$mac,{mac},10\r\n"
            self.ser.write(cmd.encode())
            time.sleep(0.1)
            
            # 读取返回数据
            response = self.ser.readline().decode().strip()
            # 解析 "mc,d4cf8914d31a,5,2.46m" 格式的数据
            parts = response.split(',')
            if len(parts) == 4 and parts[0] == 'mc' and parts[1] == mac:
                # 去掉末尾的'm'并转换为浮点数
                distance = float(parts[3].rstrip('m'))
                return distance
            return None
        except Exception as e:
            print(f"测距错误: {e}")
            return None

    def get_car_position(self):
        """获取小车当前位置"""
        try:
            response = self.api_client.get_localization()
            if response and response.get('code') == 0:
                loc_info = response.get('data', {}).get('localization_info', {})
                if loc_info and 'position' in loc_info:
                    position = loc_info['position']
                    return np.array([
                        position[0],  # x
                        position[1],  # y
                        1.29  # 固定高度
                    ])
            return None
        except Exception as e:
            print(f"获取位置失败: {e}")
            return None

    def collect_calibration_point(self, num_samples=5):
        """
        收集一个标定点的数据
        :param num_samples: 采样次数
        :return: (平均位置, 平均距离列表)
        """
        positions = []
        distances = {mac: [] for mac in self.anchors_mac}
        
        print("\n开始采集数据...")
        for i in range(num_samples):
            print(f"\n第 {i+1}/{num_samples} 次采样:")
            
            # 获取位置
            pos = self.get_car_position()
            if pos is not None:
                print(f"位置: X={pos[0]:.4f}, Y={pos[1]:.4f}, Z={pos[2]:.4f}")
                positions.append(pos)
            
            # 获取每个锚点的距离
            for mac in self.anchors_mac:
                # 清空串口缓冲区
                self.ser.reset_input_buffer()
                
                dist = self.request_distance(mac)
                if dist is not None:
                    print(f"锚点 {mac} 距离: {dist:.4f}m")
                    distances[mac].append(dist)
                else:
                    print(f"锚点 {mac} 距离获取失败")
            
            time.sleep(0.5)  # 短暂延时，确保串口和系统稳定

        if not positions:
            print("没有收集到有效的位置数据")
            return None, None

        # 计算平均位置
        avg_pos = np.mean(positions, axis=0)
        print(f"\n最终平均位置: X={avg_pos[0]:.4f}, Y={avg_pos[1]:.4f}, Z={avg_pos[2]:.4f}")

        # 计算平均距离
        avg_distances = []
        for mac in self.anchors_mac:
            valid_distances = [d for d in distances[mac] if d is not None]
            if valid_distances:
                avg_dist = statistics.median(valid_distances)
                print(f"锚点 {mac} 平均距离: {avg_dist:.4f}m")
                avg_distances.append(avg_dist)
            else:
                print(f"锚点 {mac} 没有有效距离数据")
                avg_distances.append(np.nan)
        


        return avg_pos, avg_distances

    def calibrate_anchors(self, estimate_bias=True, huber_delta=0.5, lam=0.1):
        """标定锚点位置"""
        dataset = []
        
        # 检查初始锚点数量是否匹配
        if len(self.anchors_init) != len(self.anchors_mac):
            print(f"错误：初始锚点数量 ({len(self.anchors_init)}) 与MAC地址数量 ({len(self.anchors_mac)}) 不匹配")
            return
        
        try:
            while True:
                input("\n将小车移动到新位置，按Enter开始采集数据...")
                print("正在采集数据，请保持小车静止...")
                
                pos, distances = self.collect_calibration_point(num_samples=5)
                if pos is None or distances is None:
                    print("数据采集失败，请重试")
                    continue
                    
                print("\n采集结果:")
                print(f"位置: X={pos[0]:.3f}, Y={pos[1]:.3f}, Z={pos[2]:.3f}")
                for mac, dist in zip(self.anchors_mac, distances):
                    print(f"锚点 {mac} -> {dist:.3f}m")
                
                dataset.append((pos, distances))
                
                if input("\n继续采集新的位置? (y/n): ").lower() != 'y':
                    break

        except KeyboardInterrupt:
            print("\n采集过程被中断")
        
        if not dataset:
            print("没有收集到有效数据，无法进行标定")
            return
        
        # 转换为矩阵格式
        poses = np.array([d[0] for d in dataset])
        ranges = np.array([d[1] for d in dataset]).T  # (N,M)
        
        # 执行标定
        anchors_hat, biases_hat, result = self.calibrate_anchors_with_init(
            poses, ranges, self.anchors_init,
            estimate_bias=estimate_bias,
            huber_delta=huber_delta,
            lam=lam
        )
        
        # 输出结果
        print("\n===== 标定结果 =====")
        for mac, init, hat, b in zip(self.anchors_mac, self.anchors_init, anchors_hat, biases_hat):
            print(f"锚点 {mac}")
            print(f"  初始: {init}")
            print(f"  标定: {hat}")
            print(f"  偏置: {b:.3f} m\n")
        
        # 保存结果
        np.savez("anchors_calibrated.npz",
                anchors=anchors_hat,
                biases=biases_hat,
                macs=self.anchors_mac,
                poses=poses,
                ranges=ranges)
        print("结果已保存至 anchors_calibrated.npz")

    @staticmethod
    def huber_loss(r, delta=0.5):
        """Huber鲁棒残差缩放"""
        return np.where(np.abs(r) <= delta, r, delta * np.sign(r))

    def calibrate_anchors_with_init(self, poses, ranges, anchors_init, estimate_bias=True, huber_delta=0.5, lam=0.1):
        """
        双阶段锚点标定
        ----------
        poses : ndarray (M,3)
            小车已知位置 (M个点, z固定=1.29)
        ranges : ndarray (N,M)
            测距数据 (N个锚 × M个点)
        anchors_init : ndarray (N,3)
            初始锚点坐标
        estimate_bias : bool
            是否估计偏置 b_i
        huber_delta : float
            Huber损失阈值
        lam : float
            偏置L1正则化系数
        """
        # 数据检查和预处理
        print("\n=== 数据检查 ===")
        print(f"采集点数量: {len(poses)}")
        print(f"锚点数量: {len(self.anchors_mac)}")
        print(f"poses shape: {poses.shape}")
        print(f"ranges shape: {ranges.shape}")
        print(f"anchors_init shape: {anchors_init.shape}")
        
        # 检查是否有无效值
        if np.any(np.isnan(poses)) or np.any(np.isnan(ranges)):
            raise ValueError("数据中包含NaN值")
        if np.any(np.isinf(poses)) or np.any(np.isinf(ranges)):
            raise ValueError("数据中包含Inf值")
            
        # 确保维度匹配
        N, M = ranges.shape
        if N != len(anchors_init):
            raise ValueError(f"锚点数量不匹配: ranges中为{N}个，初始值中为{len(anchors_init)}个")
        if M != len(poses):
            raise ValueError(f"测量点数量不匹配: ranges中为{M}个，poses中为{len(poses)}个")
        
        print("\n=== 参数初始化 ===")
        d = 3
        anchors0 = np.array(anchors_init[:N], dtype=float)  # 只使用需要的锚点数量
        biases0 = np.zeros(N)
        print(f"anchors0 shape: {anchors0.shape}")
        print(f"biases0 shape: {biases0.shape}")
        
        # 参数向量
        x0 = np.hstack([anchors0.ravel(), biases0])
        
        def residuals(params):
            anchors = params[:N*d].reshape(N, d)
            biases = params[N*d:] if estimate_bias else np.zeros(N)
            res = []
            for i in range(N):
                pred = np.linalg.norm(poses - anchors[i], axis=1) + biases[i]
                r = ranges[i] - pred
                r = self.huber_loss(r, delta=huber_delta)
                res.extend(r)
            if estimate_bias and lam > 0:
                res.extend(np.sqrt(lam) * biases)
            return np.array(res)
        
        result = least_squares(residuals, x0, method="lm", max_nfev=200)
        anchors_hat = result.x[:N*d].reshape(N, d)
        biases_hat = result.x[N*d:] if estimate_bias else np.zeros(N)
        return anchors_hat, biases_hat, result

def main():
    # 配置参数
    SERVER_URL = "http://192.168.3.6:10000"  # 替换为实际的服务器地址
    SERIAL_PORT = "/dev/ttyTHS1"  # 替换为实际的串口端口
    
    # 创建标定对象
    calibrator = UWBCalibration(SERVER_URL, SERIAL_PORT)
    
    # 连接串口
    if not calibrator.connect_serial():
        return
    
    try:
        # 执行标定
        calibrator.calibrate_anchors()
    finally:
        # 清理资源
        calibrator.close_serial()

if __name__ == "__main__":
    main()
