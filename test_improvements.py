#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的UWB定位算法
演示残差分析改进效果
"""

import numpy as np
import sys
import os

# 添加当前目录到路径以导入uwbtest模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from uwbtest import UWBCalibration

def test_localization_improvements():
    """
    测试定位算法的改进效果
    """
    print("=== UWB定位算法改进测试 ===\n")
    
    # 创建测试用的UWBCalibration实例（不需要真实的串口和API）
    calibrator = UWBCalibration("http://dummy", "/dev/dummy")
    
    # 模拟一些测试数据
    # 假设我们有3个锚点和对应的距离测量
    test_anchors = np.array([
        [-0.15, 2.19, 1.4],      # 锚点0
        [-6.2474, -8.8274, 2.38], # 锚点1  
        [-3.2080, -3.1134, 2.68]  # 锚点2
    ])
    
    # 模拟距离测量（包含一些误差和偏置）
    test_distances = np.array([1.785, 3.750, 6.380])
    
    # 模拟偏置（从标定中获得）
    test_biases = np.array([0.1, -0.05, 0.08])
    
    print("测试数据:")
    print(f"锚点坐标:\n{test_anchors}")
    print(f"测量距离: {test_distances}")
    print(f"估计偏置: {test_biases}")
    print()
    
    # 测试1: 不使用偏置补偿的定位
    print("=== 测试1: 不使用偏置补偿 ===")
    try:
        est1, weights1, iter1 = calibrator.robust_localize(
            test_anchors, test_distances,
            z_fixed=0.56,
            method="huber",
            delta=2.0,
            max_iter=20,
            tol=1e-4,
            k_nearest=3,
            biases=None
        )
        print(f"估计位置: [{est1[0]:.3f}, {est1[1]:.3f}, {est1[2]:.3f}]")
        print(f"迭代次数: {iter1}")
    except Exception as e:
        print(f"测试1失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试2: 使用偏置补偿的定位
    print("=== 测试2: 使用偏置补偿 ===")
    try:
        est2, weights2, iter2 = calibrator.robust_localize(
            test_anchors, test_distances,
            z_fixed=0.56,
            method="huber", 
            delta=2.0,
            max_iter=20,
            tol=1e-4,
            k_nearest=3,
            biases=test_biases
        )
        print(f"估计位置: [{est2[0]:.3f}, {est2[1]:.3f}, {est2[2]:.3f}]")
        print(f"迭代次数: {iter2}")
    except Exception as e:
        print(f"测试2失败: {e}")
    
    print("\n=== 改进总结 ===")
    print("主要改进点:")
    print("1. 修复了雅可比矩阵计算错误")
    print("2. 改进了收敛判断条件（位置变化 + 代价函数变化）")
    print("3. 添加了偏置补偿功能")
    print("4. 增加了详细的迭代过程监控")
    print("5. 修复了定位测试中使用错误锚点坐标的问题")
    print("6. 添加了RMS残差评估和警告")

def test_convergence_monitoring():
    """
    测试收敛监控功能
    """
    print("\n=== 收敛监控测试 ===")
    
    # 这里可以添加更多的收敛测试
    print("收敛监控改进:")
    print("- 同时监控位置变化和代价函数变化")
    print("- 提供详细的迭代信息")
    print("- 检测过早收敛问题")
    print("- 提供残差质量评估")

if __name__ == "__main__":
    test_localization_improvements()
    test_convergence_monitoring()
    
    print("\n=== 使用建议 ===")
    print("1. 确保使用标定后的锚点坐标进行定位")
    print("2. 如果有标定偏置，务必在定位时使用")
    print("3. 监控RMS残差，如果>20cm需要检查数据质量")
    print("4. 根据环境调整Huber/Tukey参数delta")
    print("5. 如果收敛困难，可以增加max_iter或放宽tol")
